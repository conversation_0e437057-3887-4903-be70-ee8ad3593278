using Application.Abstraction;
using Application.Services.Events;
using Domain.Entities;
using Infrastructure.Persistence;
using Infrastructure.RuleEngine;
using Infrastructure.Tests.RuleEngine;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Infrastructure.Tests.Integration;

/// <summary>
/// Integrační testy pro komplexní obchodní pravidla s reálnými daty z databáze.
/// </summary>
public class ComplexRuleIntegrationTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly CalculationEngine _engine;
    private readonly ServiceProvider _serviceProvider;

    public ComplexRuleIntegrationTests()
    {
        // Nastavení in-memory databáze
        var services = new ServiceCollection();

        // Registrace mock služeb
        var mockCurrentUserService = new Mock<ICurrentUserService>();
        mockCurrentUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        services.AddSingleton(mockCurrentUserService.Object);

        // Vytvoříme jednoduchou implementaci DomainEventPublisher pro testy
        var mockMediator = new Mock<SharedKernel.Abstractions.Mediator.IMediator>();
        var domainEventPublisher = new DomainEventPublisher(mockMediator.Object);
        services.AddSingleton(domainEventPublisher);

        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<ApplicationDbContext>();

        // Nastavení RuleEngine
        var entityTypeMap = new Dictionary<string, Type>
        {
            ["Order"] = typeof(Order),
            ["OrderItem"] = typeof(OrderItem),
            ["Invoice"] = typeof(Invoice),
            ["InvoiceItem"] = typeof(InvoiceItem)
        };

        var dataProvider = new TestRuleDataProvider();
        var expressionBuilder = new ExpressionBuilder(dataProvider, entityTypeMap);
        var mockLogger = new Mock<ILogger<CalculationEngine>>();
        _engine = new CalculationEngine(expressionBuilder, entityTypeMap, mockLogger.Object);

        // Naplnění databáze testovacími daty
        SeedTestData();
    }

    [Fact]
    public async Task Execute_ComplexCustomerLoyaltyRule_WithRealData_WorksCorrectly()
    {
        // Arrange - Získáme komplexní pravidlo z databáze
        var loyaltyRule = await _context.Set<BusinessRule>()
            .FirstOrDefaultAsync(r => r.Name.Contains("Věrnostní sleva"));

        Assert.NotNull(loyaltyRule);

        // Získáme objednávku z databáze
        var order = await _context.Set<Order>()
            .Include(o => o.Items)
            .FirstOrDefaultAsync();

        Assert.NotNull(order);

        // Act - Vykonáme pravidlo na reálné objednávce
        var discountPercentage = _engine.Execute(loyaltyRule, order);

        // Assert - Ověříme, že pravidlo vrátilo validní hodnotu
        Assert.True((decimal)discountPercentage >= 0m);
        Assert.True((decimal)discountPercentage <= 20m);
    }

    [Fact]
    public async Task Execute_RelatedAggregationRule_WithMultipleCustomers_WorksCorrectly()
    {
        // Arrange - Vytvoříme pravidlo pro testování agregace
        var aggregationRule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Test agregace faktur zákazníka",
            TargetEntityName = "Order",
            RootNode = new RelatedAggregationNode
            {
                SourceEntityName = "Order",
                TargetEntityName = "Invoice",
                RelationshipProperty = "CustomerId",
                AggregationType = AggregationType.Sum,
                AggregationField = "TotalAmount"
            }
        };

        // Získáme objednávky různých zákazníků
        var orders = await _context.Set<Order>().ToListAsync();

        // Act & Assert - Testujeme pravidlo na všech objednávkách
        foreach (var order in orders)
        {
            var result = _engine.Execute(aggregationRule, order);
            
            // ExpressionBuilder simuluje hodnotu 75000m pro Sum
            Assert.Equal(75000m, result);
        }
    }

    [Fact]
    public async Task Validate_ComplexRuleFromDatabase_IsValid()
    {
        // Arrange - Získáme komplexní pravidlo z databáze
        var loyaltyRule = await _context.BusinessRules
            .FirstOrDefaultAsync(r => r.Name.Contains("Věrnostní sleva"));

        Assert.NotNull(loyaltyRule);

        // Act
        var validationResult = _engine.ValidateRule(loyaltyRule);

        // Assert
        Assert.True(validationResult.IsValid);
        Assert.Null(validationResult.ErrorMessage);
    }

    [Fact]
    public async Task Execute_MultipleRulesOnSameOrder_WorksCorrectly()
    {
        // Arrange - Získáme všechna aktivní pravidla pro Order
        var orderRules = await _context.Set<BusinessRule>()
            .Where(r => r.TargetEntityName == "Order" && r.IsActive)
            .ToListAsync();

        var order = await _context.Set<Order>()
            .Include(o => o.Items)
            .FirstOrDefaultAsync();

        Assert.NotNull(order);
        Assert.True(orderRules.Count > 0);

        // Act - Vykonáme všechna pravidla na stejné objednávce
        var results = new Dictionary<string, object>();
        
        foreach (var rule in orderRules)
        {
            try
            {
                var result = _engine.Execute(rule, order);
                results[rule.Name] = result;
            }
            catch (Exception ex)
            {
                // Zalogujeme chybu, ale nepřerušíme test
                results[rule.Name] = $"Error: {ex.Message}";
            }
        }

        // Assert - Ověříme, že všechna pravidla vrátila nějaký výsledek
        Assert.True(results.Count > 0);
        
        foreach (var result in results)
        {
            Assert.NotNull(result.Value);
            // Výpis pro debugging
            Console.WriteLine($"Pravidlo '{result.Key}': {result.Value}");
        }
    }

    [Fact]
    public async Task Execute_ComplexRule_PerformanceTest()
    {
        // Arrange
        var loyaltyRule = await _context.Set<BusinessRule>()
            .FirstOrDefaultAsync(r => r.Name.Contains("Věrnostní sleva"));

        var orders = await _context.Set<Order>()
            .Include(o => o.Items)
            .ToListAsync();

        Assert.NotNull(loyaltyRule);
        Assert.True(orders.Count > 0);

        // Act - Měříme výkon vykonání pravidla na všech objednávkách
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        var results = new List<object>();
        foreach (var order in orders)
        {
            var result = _engine.Execute(loyaltyRule, order);
            results.Add(result);
        }
        
        stopwatch.Stop();

        // Assert - Ověříme, že výkon je přijatelný (méně než 1 sekunda pro všechny objednávky)
        Assert.True(stopwatch.ElapsedMilliseconds < 1000, 
            $"Vykonání pravidla trvalo {stopwatch.ElapsedMilliseconds}ms, což je příliš dlouho");
        
        Assert.Equal(orders.Count, results.Count);
        Console.WriteLine($"Vykonání pravidla na {orders.Count} objednávkách trvalo {stopwatch.ElapsedMilliseconds}ms");
    }

    /// <summary>
    /// Naplní databázi testovacími daty.
    /// </summary>
    private void SeedTestData()
    {
        // Vytvoříme zákazníky
        var customer1 = Guid.NewGuid();
        var customer2 = Guid.NewGuid();

        // Vytvoříme objednávky
        var orders = new List<Order>
        {
            new Order
            {
                Id = Guid.NewGuid(),
                CustomerId = customer1,
                CustomerName = "Zákazník 1",
                CustomerEmail = "<EMAIL>",
                OrderNumber = "ORD-001",
                OrderDate = DateTime.Now.AddDays(-10),
                TotalAmount = 25000m,
                Status = OrderStatus.Delivered
            },
            new Order
            {
                Id = Guid.NewGuid(),
                CustomerId = customer2,
                CustomerName = "Zákazník 2", 
                CustomerEmail = "<EMAIL>",
                OrderNumber = "ORD-002",
                OrderDate = DateTime.Now.AddDays(-5),
                TotalAmount = 15000m,
                Status = OrderStatus.Processing
            }
        };

        // Vytvoříme faktury
        var invoices = new List<Invoice>
        {
            new Invoice
            {
                Id = Guid.NewGuid(),
                CustomerId = customer1,
                CustomerName = "Zákazník 1",
                CustomerEmail = "<EMAIL>",
                InvoiceNumber = "INV-001",
                IssueDate = DateTime.Now.AddDays(-30),
                DueDate = DateTime.Now.AddDays(-15),
                TotalAmount = 30000m,
                Status = InvoiceStatus.Paid
            },
            new Invoice
            {
                Id = Guid.NewGuid(),
                CustomerId = customer1,
                CustomerName = "Zákazník 1",
                CustomerEmail = "<EMAIL>", 
                InvoiceNumber = "INV-002",
                IssueDate = DateTime.Now.AddDays(-60),
                DueDate = DateTime.Now.AddDays(-45),
                TotalAmount = 45000m,
                Status = InvoiceStatus.Paid
            }
        };

        // Vytvoříme komplexní pravidlo
        var complexRule = new BusinessRule
        {
            Id = Guid.NewGuid(),
            Name = "Věrnostní sleva podle celkové hodnoty faktur",
            Description = "Sleva podle celkové hodnoty faktur zákazníka za poslední rok",
            TargetEntityName = "Order",
            TargetProperty = "DiscountPercentage",
            SchemaVersion = "1.0",
            RootNode = new RelatedAggregationNode
            {
                SourceEntityName = "Order",
                TargetEntityName = "Invoice", 
                RelationshipProperty = "CustomerId",
                AggregationType = AggregationType.Sum,
                AggregationField = "TotalAmount"
            },
            IsActive = true
        };

        // Uložíme data do databáze
        _context.Set<Order>().AddRange(orders);
        _context.Set<Invoice>().AddRange(invoices);
        _context.Set<BusinessRule>().Add(complexRule);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context?.Dispose();
        _serviceProvider?.Dispose();
    }
}
