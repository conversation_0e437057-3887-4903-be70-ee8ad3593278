# Fluent API pro Result<T>

Fluent API rozšiřuje Result<T> pattern o metody pro funkcionální programování, které umož<PERSON>ují elegantní řetězení operací a zjednodušují běžné vzory práce s Result objekty.

## Přehled metod

### Transformační metody

#### Map<TResult>(Func<T, TResult> mapper)
Mapuje úspěšný result na jiný typ. Pokud je result neúspěšný, vrátí chyby beze změny.

```csharp
var result = Result<int>.Ok(42);
var stringResult = result.Map(x => x.ToString()); // Result<string>.Ok("42")

var errorResult = Result<int>.Error("Chyba");
var mappedError = errorResult.Map(x => x.ToString()); // Result<string>.Error("Chyba")
```

#### Bind<TResult>(Func<T, Result<TResult>> binder)
Kombinuje dva results pomocí binderu. Umožňuje řetězení operací, které mohou selhat.

```csharp
var result = Result<int>.Ok(42);
var boundResult = result.Bind(x => 
    x > 0 ? Result<string>.Ok(x.ToString()) : Result<string>.Error("Záporné číslo"));
```

### Akční metody

#### OnSuccess(Action<T> action)
Provede akci pouze pokud je result úspěšný. Vrací původní result pro řetězení.

```csharp
var result = Result<string>.Ok("test")
    .OnSuccess(value => Console.WriteLine($"Úspěch: {value}"))
    .OnSuccess(value => LogSuccess(value));
```

#### OnError(Action<string[]> action)
Provede akci pouze pokud result není úspěšný. Vrací původní result pro řetězení.

```csharp
var result = Result<string>.Error("Chyba")
    .OnError(errors => Console.WriteLine($"Chyby: {string.Join(", ", errors)}"))
    .OnError(errors => LogErrors(errors));
```

### Factory metody s validací

#### Create(T data, Func<T?, bool> validator, string errorMessage)
Vytvoří result s jednou validací.

```csharp
var result = Result<string>.Create("test", 
    x => !string.IsNullOrEmpty(x), 
    "String nesmí být prázdný");
```

#### Create(T data, params (Func<T?, bool> validator, string error)[] validations)
Vytvoří result s více validacemi.

```csharp
var result = Result<string>.Create("test",
    (x => !string.IsNullOrEmpty(x), "String nesmí být prázdný"),
    (x => x?.Length < 100, "String je příliš dlouhý"));
```

### Convenience metody

#### Implicit conversion
Automatický převod hodnoty na úspěšný Result.

```csharp
Result<string> result = "Hello World!"; // Ekvivalent Result<string>.Ok("Hello World!")
```

#### GetValueOrThrow()
Extrahuje hodnotu nebo vyhodí výjimku.

```csharp
var result = Result<string>.Ok("test");
var value = result.GetValueOrThrow(); // "test"

var errorResult = Result<string>.Error("Chyba");
var value2 = errorResult.GetValueOrThrow(); // Vyhodí InvalidOperationException
```

#### GetValueOrDefault(T? defaultValue = default)
Extrahuje hodnotu nebo vrátí výchozí hodnotu.

```csharp
var result = Result<string>.Ok("test");
var value = result.GetValueOrDefault("default"); // "test"

var errorResult = Result<string>.Error("Chyba");
var value2 = errorResult.GetValueOrDefault("default"); // "default"
```

## Příklady použití

### Před a po - Mapování dat

**PŘED (bez fluent API):**
```csharp
public async Task<Result<string>> GetUserDisplayName(int userId)
{
    var userResult = await GetUser(userId);
    
    if (!userResult.Succeeded)
        return Result<string>.Error(userResult.Errors);
        
    if (userResult.Data == null)
        return Result<string>.Error("User not found");
        
    var displayName = $"{userResult.Data.FirstName} {userResult.Data.LastName}";
    return Result<string>.Ok(displayName);
}
```

**PO (s fluent API):**
```csharp
public async Task<Result<string>> GetUserDisplayName(int userId)
{
    var result = await GetUser(userId);
    
    return result
        .Map(user => $"{user.FirstName} {user.LastName}")
        .OnSuccess(name => Console.WriteLine($"Generated display name: {name}"))
        .OnError(errors => Console.WriteLine($"Failed to get user: {string.Join(", ", errors)}"));
}
```

### Řetězení operací

```csharp
var result = Result<int>.Ok(42)
    .Map(x => x * 2)                    // Result<int>.Ok(84)
    .OnSuccess(x => Console.WriteLine($"Doubled: {x}"))
    .Map(x => x.ToString())             // Result<string>.Ok("84")
    .OnSuccess(s => Console.WriteLine($"As string: {s}"));
```

### Validace s více podmínkami

```csharp
public Result<User> CreateUser(string firstName, string lastName, string email)
{
    var user = new User { FirstName = firstName, LastName = lastName, Email = email };
    
    return Result<User>.Create(user,
        (u => !string.IsNullOrWhiteSpace(u?.FirstName), "First name is required"),
        (u => !string.IsNullOrWhiteSpace(u?.LastName), "Last name is required"),
        (u => !string.IsNullOrWhiteSpace(u?.Email) && u.Email.Contains("@"), "Valid email is required")
    );
}
```

### Zjednodušení API služeb

**PŘED:**
```csharp
public async Task<IResult> GetEntity<T>(object id)
{
    if (id == null)
        return Results.BadRequest("ID nesmí být null");

    var result = await _service.GetByIdAsync<T>(id);

    if (!result.Succeeded)
        return Results.BadRequest(result.Errors);

    if (result.Data == null)
        return Results.NotFound();

    return Results.Ok(result.Data);
}
```

**PO:**
```csharp
public async Task<IResult> GetEntity<T>(object id)
{
    if (id == null)
        return Results.BadRequest("ID nesmí být null");

    var result = await _service.GetByIdAsync<T>(id);
    
    return result
        .OnError(errors => _logger.LogError("Entity retrieval failed: {Errors}", string.Join(", ", errors)))
        .Succeeded && result.Data != null
            ? Results.Ok(result.Data)
            : result.Succeeded 
                ? Results.NotFound() 
                : Results.BadRequest(result.Errors);
}
```

## Výhody fluent API

1. **Čitelnější kód** - Operace se čtou zleva doprava jako příběh
2. **Méně boilerplate kódu** - Eliminuje opakující se kontroly `Succeeded`
3. **Funkcionální styl** - Podporuje funkcionální programování v C#
4. **Bezpečné řetězení** - Chyby se automaticky propagují řetězcem
5. **Lepší testovatelnost** - Jednotlivé transformace lze snadno testovat
6. **Konzistentní error handling** - Jednotný způsob zpracování chyb

## Kompatibilita

Fluent API je plně kompatibilní s existujícím kódem. Všechny stávající metody (`Ok`, `Error`, `OkAsync`, `ErrorAsync`) zůstávají beze změny.

## Testování

Kompletní sada unit testů je k dispozici v `SharedKernel.Tests/Models/ResultFluentApiTests.cs` a pokrývá všechny scénáře použití fluent API metod.
