namespace SharedKernel.Domain;

/// <summary>
/// Abstraktní třída pro entity, které vyžadují sledování změn.
/// Rozšiřuje základní entitu o vlastnosti pro sledování vytvoření a poslední modifikace.
/// Používá DateTimeOffset pro správnou práci s časovými pásmy.
/// </summary>
/// <typeparam name="T">Typ identifikátoru entity (např. int, Guid, string)</typeparam>
public abstract class BaseTrackableEntity<T> : BaseEntity<T>, ITrackableEntity<T>
{
    /// <summary>
    /// Datum a čas vytvoření entity.
    /// Používá DateTimeOffset pro správnou práci s časovými pásmy.
    /// </summary>
    public DateTimeOffset? CreatedAt { get; set; }

    /// <summary>
    /// Identifikátor uživatele, který entitu vytvořil.
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Datum a čas poslední modifikace entity.
    /// Používá DateTimeOffset pro správnou práci s časovými pásmy.
    /// </summary>
    public DateTimeOffset? ModifiedAt { get; set; }

    /// <summary>
    /// Identifikátor uživatele, který entitu naposledy modifikoval.
    /// </summary>
    public string? ModifiedBy { get; set; }
}