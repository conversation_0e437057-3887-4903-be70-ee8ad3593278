namespace SharedKernel.Models;

/// <summary>
/// Základní Result pattern pro reprezentaci výsledku operace.
/// Poskytuje společné vlastnosti pro všechny typy výsledků.
/// </summary>
public abstract record Result
{
    /// <summary>
    /// Indikuje, zda operace proběhla úspěšně.
    /// </summary>
    public bool Succeeded { get; protected init; }

    /// <summary>
    /// Seznam chybových zpráv v případě neúspěchu.
    /// </summary>
    public string[] Errors { get; protected init; } = Array.Empty<string>();

    /// <summary>
    /// Konstruktor pro odvozené třídy.
    /// </summary>
    /// <param name="succeeded">Indikuje úspěch operace</param>
    /// <param name="errors">Chybové zprávy</param>
    protected Result(bool succeeded, string[] errors)
    {
        Succeeded = succeeded;
        Errors = errors;
    }
}

/// <summary>
/// Obecný Result pattern pro reprezentaci výsledku operace s daty.
/// Umožňuje jednotný způsob vracení úspěšných i neúspěšných výsledků.
/// </summary>
/// <typeparam name="T">Typ dat vracených při úspěšném výsledku</typeparam>
public record Result<T> : Result
{
    /// <summary>
    /// Data vrácená při úspěšném výsledku.
    /// </summary>
    public T? Data { get; }

    protected Result(bool succeeded, T? data, string[] errors) : base(succeeded, errors)
    {
        Data = data;
    }

    /// <summary>
    /// Vytvoří úspěšný výsledek s daty.
    /// </summary>
    /// <param name="data">Data k vrácení</param>
    /// <returns>Úspěšný Result s daty</returns>
    public static Result<T> Ok(T? data)
        => new Result<T>(true, data, Array.Empty<string>());

    /// <summary>
    /// Vytvoří neúspěšný výsledek s chybovými zprávami.
    /// </summary>
    /// <param name="errors">Chybové zprávy</param>
    /// <returns>Neúspěšný Result s chybami</returns>
    public static Result<T> Error(params string[] errors)
        => new Result<T>(false, default, errors);

    /// <summary>
    /// Asynchronně vytvoří úspěšný výsledek s daty.
    /// </summary>
    /// <param name="data">Data k vrácení</param>
    /// <returns>Task s úspěšným Result</returns>
    public static Task<Result<T>> OkAsync(T? data)
        => Task.FromResult(Ok(data));

    /// <summary>
    /// Asynchronně vytvoří neúspěšný výsledek s chybovými zprávami.
    /// </summary>
    /// <param name="errors">Chybové zprávy</param>
    /// <returns>Task s neúspěšným Result</returns>
    public static Task<Result<T>> ErrorAsync(params string[] errors)
        => Task.FromResult(Error(errors));
}
