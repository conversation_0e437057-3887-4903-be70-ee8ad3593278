using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace SharedKernel.Utilities;

/// <summary>
/// Utility metody pro práci s řetězci.
/// Poskytuje obecné funkcionality pro manipulaci s textem.
/// </summary>
public static class StringUtilities
{
    /// <summary>
    /// Převede řetězec na slug (URL-friendly formát).
    /// </summary>
    /// <param name="input">Vstupní řetězec</param>
    /// <param name="maxLength">Maximální délka výsledku</param>
    /// <returns>Slug řetězec</returns>
    public static string ToSlug(string input, int maxLength = 50)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;

        // Převod na malá písmena a odstranění diakritiky
        var normalizedString = input.ToLowerInvariant()
            .Normalize(NormalizationForm.FormD);

        var stringBuilder = new StringBuilder();
        foreach (var c in normalizedString)
        {
            var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != UnicodeCategory.NonSpacingMark)
            {
                stringBuilder.Append(c);
            }
        }

        var withoutDiacritics = stringBuilder.ToString().Normalize(NormalizationForm.FormC);

        // Nahrazení nepovolených znaků pomlčkami
        var slug = Regex.Replace(withoutDiacritics, @"[^a-z0-9\s-]", "");
        slug = Regex.Replace(slug, @"\s+", "-").Trim('-');
        slug = Regex.Replace(slug, @"-+", "-");

        // Omezení délky
        if (slug.Length > maxLength)
        {
            slug = slug.Substring(0, maxLength).Trim('-');
        }

        return slug;
    }

    /// <summary>
    /// Zkrátí řetězec na zadanou délku a přidá trojtečku.
    /// </summary>
    /// <param name="input">Vstupní řetězec</param>
    /// <param name="maxLength">Maximální délka</param>
    /// <param name="suffix">Přípona (výchozí "...")</param>
    /// <returns>Zkrácený řetězec</returns>
    public static string Truncate(string input, int maxLength, string suffix = "...")
    {
        if (string.IsNullOrWhiteSpace(input) || input.Length <= maxLength)
            return input ?? string.Empty;

        var truncatedLength = maxLength - suffix.Length;
        if (truncatedLength <= 0)
            return suffix;

        return input.Substring(0, truncatedLength) + suffix;
    }

    /// <summary>
    /// Převede řetězec na PascalCase.
    /// </summary>
    /// <param name="input">Vstupní řetězec</param>
    /// <returns>Řetězec v PascalCase</returns>
    public static string ToPascalCase(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;

        var words = input.Split(new[] { ' ', '-', '_' }, StringSplitOptions.RemoveEmptyEntries);
        var result = new StringBuilder();

        foreach (var word in words)
        {
            if (word.Length > 0)
            {
                result.Append(char.ToUpperInvariant(word[0]));
                if (word.Length > 1)
                {
                    result.Append(word.Substring(1).ToLowerInvariant());
                }
            }
        }

        return result.ToString();
    }

    /// <summary>
    /// Převede řetězec na camelCase.
    /// </summary>
    /// <param name="input">Vstupní řetězec</param>
    /// <returns>Řetězec v camelCase</returns>
    public static string ToCamelCase(string input)
    {
        var pascalCase = ToPascalCase(input);
        if (string.IsNullOrEmpty(pascalCase))
            return string.Empty;

        return char.ToLowerInvariant(pascalCase[0]) + pascalCase.Substring(1);
    }

    /// <summary>
    /// Kontroluje, zda řetězec obsahuje pouze číslice.
    /// </summary>
    /// <param name="input">Vstupní řetězec</param>
    /// <returns>True pokud obsahuje pouze číslice</returns>
    public static bool IsNumeric(string input)
    {
        return !string.IsNullOrWhiteSpace(input) && input.All(char.IsDigit);
    }

    /// <summary>
    /// Maskuje citlivé informace v řetězci.
    /// </summary>
    /// <param name="input">Vstupní řetězec</param>
    /// <param name="visibleChars">Počet viditelných znaků na začátku a konci</param>
    /// <param name="maskChar">Znak pro maskování</param>
    /// <returns>Maskovaný řetězec</returns>
    public static string Mask(string input, int visibleChars = 2, char maskChar = '*')
    {
        if (string.IsNullOrWhiteSpace(input) || input.Length <= visibleChars * 2)
            return new string(maskChar, input?.Length ?? 0);

        var start = input.Substring(0, visibleChars);
        var end = input.Substring(input.Length - visibleChars);
        var middle = new string(maskChar, input.Length - visibleChars * 2);

        return start + middle + end;
    }
}
