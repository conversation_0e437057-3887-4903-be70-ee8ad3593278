namespace SharedKernel.Abstractions.Mediator;

/// <summary>
/// Delegát pro asynchronní zpracování požadavku, který vrací výsledek.
/// Používá se v pipeline behaviors pro předání řízení dal<PERSON>mu behavior nebo handleru.
/// </summary>
/// <typeparam name="TResponse"><PERSON><PERSON> od<PERSON>, který bude vrácen po zpracování požadavku.</typeparam>
/// <returns>Task s výsledkem zpracování požadavku.</returns>
public delegate Task<TResponse> RequestHandlerDelegate<TResponse>();
