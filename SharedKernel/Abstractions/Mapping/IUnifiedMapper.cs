using System.Linq.Expressions;

namespace SharedKernel.Abstractions.Mapping;

/// <summary>
/// Univerzální rozhraní pro mapování mezi dvěma typy.
/// Podporuje automatické mapování, konfigurované mapování a obousměrné operace.
/// </summary>
/// <typeparam name="TSource">Zdrojový typ.</typeparam>
/// <typeparam name="TTarget">Cílový typ.</typeparam>
public interface IUnifiedMapper<TSource, TTarget>
    where TSource : class
    where TTarget : class
{
    /// <summary>
    /// Mapuje zdrojový objekt na cílový objekt.
    /// </summary>
    /// <param name="source">Zdrojový objekt.</param>
    /// <param name="useConfig">Pokud true, použije konfigurované mapování, jinak automatické.</param>
    /// <returns>Nová instance cílového typu.</returns>
    TTarget Map(TSource source, bool useConfig = false);

    /// <summary>
    /// Mapuje cílový objekt zpět na zdrojový objekt.
    /// </summary>
    /// <param name="target">Cílový objekt.</param>
    /// <param name="useConfig">Pokud true, použije konfigurované mapování, jinak automatické.</param>
    /// <returns>Nová instance zdrojového typu.</returns>
    TSource MapBack(TTarget target, bool useConfig = false);

    /// <summary>
    /// Aktualizuje existující cílový objekt hodnotami ze zdrojového objektu.
    /// </summary>
    /// <param name="source">Zdrojový objekt.</param>
    /// <param name="target">Existující cílový objekt k aktualizaci.</param>
    void Update(TSource source, TTarget target);

    /// <summary>
    /// Aktualizuje existující zdrojový objekt hodnotami z cílového objektu.
    /// </summary>
    /// <param name="target">Cílový objekt.</param>
    /// <param name="source">Existující zdrojový objekt k aktualizaci.</param>
    void UpdateBack(TTarget target, TSource source);

    /// <summary>
    /// Mapuje kolekci zdrojových objektů na kolekci cílových objektů.
    /// </summary>
    /// <param name="sources">Kolekce zdrojových objektů.</param>
    /// <param name="useConfig">Pokud true, použije konfigurované mapování, jinak automatické.</param>
    /// <returns>Kolekce cílových objektů.</returns>
    IEnumerable<TTarget> MapCollection(IEnumerable<TSource> sources, bool useConfig = false);

    /// <summary>
    /// Mapuje kolekci cílových objektů zpět na kolekci zdrojových objektů.
    /// </summary>
    /// <param name="targets">Kolekce cílových objektů.</param>
    /// <param name="useConfig">Pokud true, použije konfigurované mapování, jinak automatické.</param>
    /// <returns>Kolekce zdrojových objektů.</returns>
    IEnumerable<TSource> MapBackCollection(IEnumerable<TTarget> targets, bool useConfig = false);
}

/// <summary>
/// Konfigurační rozhraní pro vlastní mapování.
/// </summary>
/// <typeparam name="TSource">Zdrojový typ.</typeparam>
/// <typeparam name="TTarget">Cílový typ.</typeparam>
public interface IMappingConfig<TSource, TTarget>
    where TSource : class
    where TTarget : class
{
    /// <summary>
    /// Definuje vlastní mapování mezi vlastnostmi zdrojového a cílového typu.
    /// </summary>
    /// <typeparam name="TPropertySource">Typ vlastnosti zdrojového objektu.</typeparam>
    /// <typeparam name="TPropertyTarget">Typ vlastnosti cílového objektu.</typeparam>
    /// <param name="sourceExpression">Expression pro získání hodnoty ze zdrojového objektu.</param>
    /// <param name="targetExpression">Expression pro nastavení hodnoty v cílovém objektu.</param>
    /// <param name="converter">Volitelná konverzní funkce pro transformaci hodnoty.</param>
    void Map<TPropertySource, TPropertyTarget>(
        Expression<Func<TSource, TPropertySource>> sourceExpression,
        Expression<Func<TTarget, TPropertyTarget>> targetExpression,
        Func<TPropertySource, TPropertyTarget>? converter = null);
}
