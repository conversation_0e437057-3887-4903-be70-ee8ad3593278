using Domain.Entities;

namespace Application.Features.Orders;

/// <summary>
/// DTO pro čtení ob<PERSON>.
/// </summary>
public class OrderDto
{
    /// <summary>
    /// Jedinečný identifikátor obje<PERSON>.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Č<PERSON>lo objedn<PERSON>vky.
    /// </summary>
    public string OrderNumber { get; set; } = string.Empty;

    /// <summary>
    /// Datum vytvoření objedn<PERSON>vky.
    /// </summary>
    public DateTime OrderDate { get; set; }

    /// <summary>
    /// ID zákazníka.
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// Jméno zákazníka.
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// Email zákazníka.
    /// </summary>
    public string CustomerEmail { get; set; } = string.Empty;

    /// <summary>
    /// St<PERSON> ob<PERSON>.
    /// </summary>
    public OrderStatus Status { get; set; }

    /// <summary>
    /// Celková částka bez DPH.
    /// </summary>
    public decimal SubTotal { get; set; }

    /// <summary>
    /// Výše DPH.
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Sleva v procentech.
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Částka slevy.
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Poštovné.
    /// </summary>
    public decimal ShippingCost { get; set; }

    /// <summary>
    /// Celková částka včetně DPH.
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Měna objednávky.
    /// </summary>
    public string Currency { get; set; } = "CZK";

    /// <summary>
    /// Poznámky k objednávce.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Dodací adresa.
    /// </summary>
    public string ShippingAddress { get; set; } = string.Empty;

    /// <summary>
    /// Město dodání.
    /// </summary>
    public string ShippingCity { get; set; } = string.Empty;

    /// <summary>
    /// PSČ dodání.
    /// </summary>
    public string ShippingPostalCode { get; set; } = string.Empty;

    /// <summary>
    /// Země dodání.
    /// </summary>
    public string ShippingCountry { get; set; } = "CZ";

    /// <summary>
    /// Očekávané datum dodání.
    /// </summary>
    public DateTime? ExpectedDeliveryDate { get; set; }

    /// <summary>
    /// Skutečné datum dodání.
    /// </summary>
    public DateTime? ActualDeliveryDate { get; set; }

    /// <summary>
    /// Datum vytvoření záznamu.
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Kdo vytvořil záznam.
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Datum poslední úpravy záznamu.
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Kdo naposledy upravil záznam.
    /// </summary>
    public string? ModifiedBy { get; set; }

    /// <summary>
    /// Verze řádku pro optimistické zamykání.
    /// </summary>
    public byte[]? RowVersion { get; set; }

    /// <summary>
    /// Celkový počet položek v objednávce.
    /// </summary>
    public int TotalItemCount { get; set; }

    /// <summary>
    /// Celková hmotnost objednávky.
    /// </summary>
    public decimal TotalWeight { get; set; }

    /// <summary>
    /// Určuje, zda je objednávka dokončená.
    /// </summary>
    public bool IsCompleted { get; set; }

    /// <summary>
    /// Určuje, zda je objednávka zrušená.
    /// </summary>
    public bool IsCancelled { get; set; }

    /// <summary>
    /// Počet dní od vytvoření objednávky.
    /// </summary>
    public int DaysFromOrder { get; set; }

    // Poznámka: Seznam položek objednávky je dostupný přes samostatný endpoint /order-items
    // Pro základní CRUD operace s objednávkami není vnořená kolekce nutná
}

/// <summary>
/// DTO pro editaci objednávky.
/// </summary>
public class OrderAddEdit
{
    /// <summary>
    /// Číslo objednávky.
    /// </summary>
    public string OrderNumber { get; set; } = string.Empty;

    /// <summary>
    /// Datum vytvoření objednávky.
    /// </summary>
    public DateTime OrderDate { get; set; }

    /// <summary>
    /// ID zákazníka.
    /// </summary>
    public Guid CustomerId { get; set; }

    /// <summary>
    /// Jméno zákazníka.
    /// </summary>
    public string CustomerName { get; set; } = string.Empty;

    /// <summary>
    /// Email zákazníka.
    /// </summary>
    public string CustomerEmail { get; set; } = string.Empty;

    /// <summary>
    /// Stav objednávky.
    /// </summary>
    public OrderStatus Status { get; set; }

    /// <summary>
    /// Celková částka bez DPH.
    /// </summary>
    public decimal SubTotal { get; set; }

    /// <summary>
    /// Výše DPH.
    /// </summary>
    public decimal TaxAmount { get; set; }

    /// <summary>
    /// Sleva v procentech.
    /// </summary>
    public decimal DiscountPercentage { get; set; }

    /// <summary>
    /// Částka slevy.
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// Poštovné.
    /// </summary>
    public decimal ShippingCost { get; set; }

    /// <summary>
    /// Celková částka včetně DPH.
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// Měna objednávky.
    /// </summary>
    public string Currency { get; set; } = "CZK";

    /// <summary>
    /// Poznámky k objednávce.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Dodací adresa.
    /// </summary>
    public string ShippingAddress { get; set; } = string.Empty;

    /// <summary>
    /// Město dodání.
    /// </summary>
    public string ShippingCity { get; set; } = string.Empty;

    /// <summary>
    /// PSČ dodání.
    /// </summary>
    public string ShippingPostalCode { get; set; } = string.Empty;

    /// <summary>
    /// Země dodání.
    /// </summary>
    public string ShippingCountry { get; set; } = "CZ";

    /// <summary>
    /// Očekávané datum dodání.
    /// </summary>
    public DateTime? ExpectedDeliveryDate { get; set; }

    /// <summary>
    /// Skutečné datum dodání.
    /// </summary>
    public DateTime? ActualDeliveryDate { get; set; }
}
