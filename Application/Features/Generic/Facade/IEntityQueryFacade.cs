using SharedKernel.Abstractions.Mediator;
using SharedKernel.Models;

namespace Application.Features.Generic.Facade;

/// <summary>
/// Fasáda pro jednoduché použití generických dotazů s automatickým odvozením typů
/// </summary>
public interface IEntityQueryFacade
{
    /// <summary>
    /// Získá entitu podle ID
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="id">ID entity</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>DTO entity nebo null pokud neexistuje</returns>
    Task<Result<object?>> GetByIdAsync<TEntity>(object id, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class;

    /// <summary>
    /// Získá všechny entity
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="specification">Volitelná specifikace pro filtrování</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Seznam DTO entit</returns>
    Task<Result<IEnumerable<object>>> GetAllAsync<TEntity>(ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class;

    /// <summary>
    /// Získá stránkovaný seznam entit
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="specification">Volitelná specifikace pro filtrování</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Stránkovaný seznam DTO entit</returns>
    Task<PagedResult<object>> GetPagedAsync<TEntity>(int pageNumber = 1, int pageSize = 10, ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class;

    /// <summary>
    /// Získá všechny entity se silně typovaným výsledkem
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <typeparam name="TDto">Typ DTO</typeparam>
    /// <param name="specification">Volitelná specifikace pro filtrování</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Seznam DTO entit</returns>
    Task<Result<List<TDto>>> GetAllAsync<TEntity, TDto>(ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
        where TDto : class;

    /// <summary>
    /// Získá stránkovaný seznam entit se silně typovaným výsledkem
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <typeparam name="TDto">Typ DTO</typeparam>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <param name="specification">Volitelná specifikace pro filtrování</param>
    /// <param name="useCache">Zda použít cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Stránkovaný seznam DTO entit</returns>
    Task<PagedResult<TDto>> GetPagedAsync<TEntity, TDto>(int pageNumber = 1, int pageSize = 10, ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
        where TDto : class;
}

/// <summary>
/// Implementace fasády pro generické dotazy
/// </summary>
public class EntityQueryFacade : IEntityQueryFacade
{
    private readonly IMediator _mediator;
    private readonly IEntityTypeRegistry _typeRegistry;

    public EntityQueryFacade(IMediator mediator, IEntityTypeRegistry typeRegistry)
    {
        _mediator = mediator;
        _typeRegistry = typeRegistry;
    }

    public async Task<Result<object?>> GetByIdAsync<TEntity>(object id, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        var typeInfo = _typeRegistry.GetEntityTypeInfo<TEntity>();
        if (typeInfo == null)
        {
            return await Result<object?>.ErrorAsync($"Entita {typeof(TEntity).Name} není registrována v EntityTypeRegistry");
        }

        // Vytvoříme generický dotaz pomocí reflection
        var queryType = typeof(Queries.GetEntityByIdQuery<>)
            .MakeGenericType(typeInfo.DtoType);

        var query = Activator.CreateInstance(queryType);
        if (query == null)
        {
            return await Result<object?>.ErrorAsync("Nepodařilo se vytvořit GetEntityByIdQuery");
        }

        // Nastavíme Id a UseCache
        var idProperty = queryType.GetProperty("Id");
        var useCacheProperty = queryType.GetProperty("UseCache");
        
        idProperty?.SetValue(query, id);
        useCacheProperty?.SetValue(query, useCache);

        // Odešleme dotaz pomocí dynamického volání
        dynamic result = await _mediator.Send((dynamic)query, cancellationToken);

        return await ConvertResultToObject(result);
    }

    public async Task<Result<IEnumerable<object>>> GetAllAsync<TEntity>(ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        var typeInfo = _typeRegistry.GetEntityTypeInfo<TEntity>();
        if (typeInfo == null)
        {
            return await Result<IEnumerable<object>>.ErrorAsync($"Entita {typeof(TEntity).Name} není registrována v EntityTypeRegistry");
        }

        // Vytvoříme generický dotaz pomocí reflection
        var queryType = typeof(Queries.GetAllEntitiesQuery<,>)
            .MakeGenericType(typeInfo.DtoType, typeInfo.EntityType);

        var query = Activator.CreateInstance(queryType);
        if (query == null)
        {
            return await Result<IEnumerable<object>>.ErrorAsync("Nepodařilo se vytvořit GetAllEntitiesQuery");
        }

        // Nastavíme vlastnosti
        var specificationProperty = queryType.GetProperty("Specification");
        var useCacheProperty = queryType.GetProperty("UseCache");
        
        specificationProperty?.SetValue(query, specification);
        useCacheProperty?.SetValue(query, useCache);

        // Odešleme dotaz pomocí dynamického volání
        dynamic result = await _mediator.Send((dynamic)query, cancellationToken);

        return await ConvertResultToEnumerable(result);
    }

    public async Task<PagedResult<object>> GetPagedAsync<TEntity>(int pageNumber = 1, int pageSize = 10, ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
    {
        var typeInfo = _typeRegistry.GetEntityTypeInfo<TEntity>();
        if (typeInfo == null)
        {
            return PagedResult<object>.Error($"Entita {typeof(TEntity).Name} není registrována v EntityTypeRegistry");
        }

        // Vytvoříme generický dotaz pomocí reflection
        var queryType = typeof(Queries.GetPagedEntitiesQuery<,>)
            .MakeGenericType(typeInfo.DtoType, typeInfo.EntityType);

        var query = Activator.CreateInstance(queryType);
        if (query == null)
        {
            return PagedResult<object>.Error("Nepodařilo se vytvořit GetPagedEntitiesQuery");
        }

        // Nastavíme vlastnosti
        var pageNumberProperty = queryType.GetProperty("PageNumber");
        var pageSizeProperty = queryType.GetProperty("PageSize");
        var specificationProperty = queryType.GetProperty("Specification");
        var useCacheProperty = queryType.GetProperty("UseCache");

        pageNumberProperty?.SetValue(query, pageNumber);
        pageSizeProperty?.SetValue(query, pageSize);
        specificationProperty?.SetValue(query, specification);
        useCacheProperty?.SetValue(query, useCache);

        // Odešleme dotaz pomocí dynamického volání
        dynamic result = await _mediator.Send((dynamic)query, cancellationToken);

        return await ConvertPagedResultToObject(result);
    }

    public async Task<Result<List<TDto>>> GetAllAsync<TEntity, TDto>(ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
        where TDto : class
    {
        var query = new Queries.GetAllEntitiesQuery<TDto, TEntity>
        {
            Specification = specification,
            UseCache = useCache
        };

        return await _mediator.Send(query, cancellationToken);
    }

    public async Task<PagedResult<TDto>> GetPagedAsync<TEntity, TDto>(int pageNumber = 1, int pageSize = 10, ISpecification<TEntity>? specification = null, bool useCache = false, CancellationToken cancellationToken = default)
        where TEntity : class
        where TDto : class
    {
        var query = new Queries.GetPagedEntitiesQuery<TDto, TEntity>
        {
            PageNumber = pageNumber,
            PageSize = pageSize,
            Specification = specification,
            UseCache = useCache
        };

        return await _mediator.Send(query, cancellationToken);
    }

    private static async Task<Result<object?>> ConvertResultToObject(object result)
    {
        var succeededProperty = result.GetType().GetProperty("Succeeded");
        var errorsProperty = result.GetType().GetProperty("Errors");
        var dataProperty = result.GetType().GetProperty("Data");

        if (succeededProperty?.GetValue(result) is true)
        {
            var data = dataProperty?.GetValue(result);
            return await Result<object?>.OkAsync(data);
        }
        else
        {
            var errors = errorsProperty?.GetValue(result) as IEnumerable<string> ?? new[] { "Neznámá chyba" };
            return await Result<object?>.ErrorAsync(errors.ToArray());
        }
    }

    private static async Task<Result<IEnumerable<object>>> ConvertResultToEnumerable(object result)
    {
        var succeededProperty = result.GetType().GetProperty("Succeeded");
        var errorsProperty = result.GetType().GetProperty("Errors");
        var dataProperty = result.GetType().GetProperty("Data");

        if (succeededProperty?.GetValue(result) is true)
        {
            var data = dataProperty?.GetValue(result);
            if (data is IEnumerable<object> enumerable)
            {
                return await Result<IEnumerable<object>>.OkAsync(enumerable);
            }
            else if (data is System.Collections.IEnumerable nonGenericEnumerable)
            {
                var objects = nonGenericEnumerable.Cast<object>();
                return await Result<IEnumerable<object>>.OkAsync(objects);
            }
            else
            {
                return await Result<IEnumerable<object>>.OkAsync(new List<object>());
            }
        }
        else
        {
            var errors = errorsProperty?.GetValue(result) as IEnumerable<string> ?? new[] { "Neznámá chyba" };
            return await Result<IEnumerable<object>>.ErrorAsync(errors.ToArray());
        }
    }

    private static async Task<PagedResult<object>> ConvertPagedResultToObject(object result)
    {
        // Nyní result je přímo PagedResult<TDto>, ne Result<PagedResult<TDto>>
        var succeededProperty = result.GetType().GetProperty("Succeeded");
        var errorsProperty = result.GetType().GetProperty("Errors");
        var itemsProperty = result.GetType().GetProperty("Items");
        var pageNumberProperty = result.GetType().GetProperty("PageNumber");
        var totalPagesProperty = result.GetType().GetProperty("TotalPages");
        var totalCountProperty = result.GetType().GetProperty("TotalCount");
        var pageSizeProperty = result.GetType().GetProperty("PageSize");

        if (succeededProperty?.GetValue(result) is true)
        {
            var items = itemsProperty?.GetValue(result) as System.Collections.IEnumerable;
            var pageNumber = (int)(pageNumberProperty?.GetValue(result) ?? 1);
            var totalCount = (int)(totalCountProperty?.GetValue(result) ?? 0);
            var pageSize = (int)(pageSizeProperty?.GetValue(result) ?? 10);

            var objectItems = items?.Cast<object>().ToList() ?? new List<object>();
            return await PagedResult<object>.OkAsync(objectItems, totalCount, pageNumber, pageSize);
        }
        else
        {
            var errors = errorsProperty?.GetValue(result) as string[] ?? new[] { "Neznámá chyba" };
            return await PagedResult<object>.ErrorAsync(errors);
        }
    }
}
