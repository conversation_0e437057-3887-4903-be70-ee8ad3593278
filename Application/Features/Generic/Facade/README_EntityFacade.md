# Entity Facade - Zjednodušené API pro generické operace

Entity Facade poskytuje zjednodušené API pro práci s generickými příkazy a dotazy, které vyžaduje pouze jeden generický parametr `TEntity` místo původních 3-4 parametrů.

## Problém

Původní generické příkazy a dotazy vyžadovaly mnoho generických parametrů:

```csharp
// Starý přístup - mnoho parametrů
CreateEntityCommand<SampleEntity, SampleAddEdit, int>
GetAllEntitiesQuery<SampleDto, SampleEntity>
GetPagedEntitiesQuery<SampleDto, SampleEntity>
UpdateEntityCommand<SampleEntity, SampleAddEdit, int>
```

## Řešení

Entity Facade automaticky odvodí související typy pomocí `EntityTypeRegistry`:

```csharp
// Nový přístup - pouze TEntity
await facade.CreateAsync<SampleEntity>(editDto);
await facade.GetAllAsync<SampleEntity>();
await facade.GetPagedAsync<SampleEntity>(1, 10);
await facade.UpdateAsync<SampleEntity>(id, editDto);
```

## Architektura

### Komponenty

1. **IEntityTypeRegistry** - Registry pro mapování typů
2. **IEntityCommandFacade** - Fasáda pro příkazy
3. **IEntityQueryFacade** - Fasáda pro dotazy  
4. **IEntityFacade** - Hlavní fasáda kombinující příkazy a dotazy

### Automatické odvození typů

Pro každou entitu `TEntity` se automaticky odvodí:
- `TDto` - typ pro čtení dat
- `TEditDto` - typ pro editaci dat
- `TKey` - typ primárního klíče

## Použití

### Základní CRUD operace

```csharp
public class SampleService
{
    private readonly IEntityFacade _facade;

    public SampleService(IEntityFacade facade)
    {
        _facade = facade;
    }

    public async Task<object> CreateSampleAsync(SampleAddEdit dto)
    {
        var result = await _facade.CreateAsync<SampleEntity>(dto);
        return result.Data; // ID nové entity
    }

    public async Task<object?> GetSampleAsync(int id)
    {
        var result = await _facade.GetByIdAsync<SampleEntity>(id, useCache: true);
        return result.Data; // SampleDto nebo null
    }

    public async Task<bool> UpdateSampleAsync(int id, SampleAddEdit dto)
    {
        var result = await _facade.UpdateAsync<SampleEntity>(id, dto);
        return result.Succeeded;
    }

    public async Task<bool> DeleteSampleAsync(int id)
    {
        var result = await _facade.DeleteAsync<SampleEntity>(id);
        return result.Succeeded;
    }
}
```

### Dotazy s filtrováním

```csharp
public async Task<IEnumerable<object>> GetActiveSamplesAsync()
{
    var specification = new ActiveSamplesSpecification();
    var result = await _facade.GetAllAsync<SampleEntity>(
        specification: specification,
        useCache: true);
    
    return result.Data ?? Enumerable.Empty<object>();
}

public async Task<PagedResult<object>> GetPagedSamplesAsync(int page, int size)
{
    var specification = new ActiveSamplesSpecification();
    var result = await _facade.GetPagedAsync<SampleEntity>(
        pageNumber: page,
        pageSize: size,
        specification: specification,
        useCache: true);
    
    return result.Data ?? new PagedResult<object>(new List<object>(), 0, page, size);
}
```

### Silně typované dotazy (pokročilé)

Pro případy, kdy potřebujete silně typované výsledky:

```csharp
public async Task<List<SampleDto>> GetTypedSamplesAsync()
{
    var specification = new ActiveSamplesSpecification();
    var result = await _facade.GetAllAsync<SampleEntity, SampleDto>(
        specification: specification,
        useCache: true);
    
    return result.Data ?? new List<SampleDto>();
}

public async Task<PagedResult<SampleDto>> GetTypedPagedSamplesAsync(int page, int size)
{
    var result = await _facade.GetPagedAsync<SampleEntity, SampleDto>(
        pageNumber: page,
        pageSize: size,
        useCache: true);
    
    return result.Data ?? new PagedResult<SampleDto>(new List<SampleDto>(), 0, page, size);
}
```

## Registrace entit

Entity se registrují automaticky v `DependencyInjection.cs`:

```csharp
private static void RegisterEntityFacade(IServiceCollection services)
{
    services.AddSingleton<IEntityTypeRegistry>(provider =>
    {
        var registry = new EntityTypeRegistry();
        
        // Automatická registrace všech entit
        var entityTypes = GetEntityTypes();
        foreach (var entityInfo in entityTypes)
        {
            registry.RegisterEntity<TEntity, TDto, TEditDto, TKey>();
        }
        
        return registry;
    });

    services.AddScoped<IEntityFacade, EntityFacade>();
}
```

## Výhody

### 1. Jednoduchost použití
```csharp
// Místo tohoto:
var command = new CreateEntityCommand<SampleEntity, SampleAddEdit, int> 
{ 
    Payload = dto 
};
var result = await mediator.Send(command);

// Stačí toto:
var result = await facade.CreateAsync<SampleEntity>(dto);
```

### 2. Méně chyb
- Automatické odvození typů eliminuje chyby v generických parametrech
- Kompilační kontrola správnosti typů

### 3. Lepší čitelnost
- Kód je kratší a jasnější
- Méně "noise" v podobě generických parametrů

### 4. Zpětná kompatibilita
- Původní generické příkazy a dotazy zůstávají funkční
- Postupná migrace je možná

## Utility metody

```csharp
// Kontrola registrace entity
if (facade.IsEntityRegistered<SampleEntity>())
{
    // Entita je registrována
}

// Získání informací o typech
var typeInfo = facade.GetEntityTypeInfo<SampleEntity>();
if (typeInfo != null)
{
    Console.WriteLine($"DTO Type: {typeInfo.DtoType.Name}");
    Console.WriteLine($"Edit DTO Type: {typeInfo.EditDtoType.Name}");
    Console.WriteLine($"Key Type: {typeInfo.KeyType.Name}");
}
```

## Omezení

1. **Reflection overhead** - Fasáda používá reflection pro vytváření generických typů
2. **Object return types** - Základní metody vrací `object` místo silně typovaných výsledků
3. **Runtime chyby** - Některé chyby se projeví až za běhu místo při kompilaci

## Kdy použít

### Použijte fasádu když:
- Potřebujete jednoduché CRUD operace
- Chcete minimalizovat generické parametry
- Pracujete s více různými entitami
- Rychlost vývoje je prioritou

### Použijte původní příkazy/dotazy když:
- Potřebujete maximální výkon
- Chcete silně typované výsledky
- Pracujete s komplexními scénáři
- Type safety je kritická

## Migrace

### Postupná migrace
```csharp
// Krok 1: Přidejte fasádu do konstruktoru
public class SampleController
{
    private readonly IMediator _mediator;
    private readonly IEntityFacade _facade; // Nové

    // Krok 2: Postupně nahrazujte volání
    public async Task<IActionResult> Create(SampleAddEdit dto)
    {
        // Staré:
        // var command = new CreateEntityCommand<SampleEntity, SampleAddEdit, int> { Payload = dto };
        // var result = await _mediator.Send(command);
        
        // Nové:
        var result = await _facade.CreateAsync<SampleEntity>(dto);
        
        return result.Succeeded ? Ok(result.Data) : BadRequest(result.Errors);
    }
}
```

## Příklady použití

Kompletní příklady najdete v `EntityFacadeUsageExamples.cs`:
- Základní CRUD operace
- Dotazy s filtrováním
- Silně typované dotazy
- Práce s registry typů
- Kompletní workflow

Entity Facade poskytuje elegantní řešení pro zjednodušení práce s generickými operacemi při zachování flexibility a výkonu původního systému.
