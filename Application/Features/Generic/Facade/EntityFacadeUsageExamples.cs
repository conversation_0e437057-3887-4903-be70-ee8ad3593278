using Application.Features.Generic.Specifications;
using Application.Features.Sample;
using Domain.Entities;

namespace Application.Features.Generic.Facade;

/// <summary>
/// Ukázkové použití EntityFacade pro jednoduché operace s entitami
/// </summary>
public class EntityFacadeUsageExamples
{
    private readonly IEntityFacade _entityFacade;

    public EntityFacadeUsageExamples(IEntityFacade entityFacade)
    {
        _entityFacade = entityFacade;
    }

    /// <summary>
    /// Ukázka základních CRUD operací s minimálními generickými parametry
    /// </summary>
    public async Task BasicCrudExampleAsync()
    {
        // === VYTVOŘENÍ ENTITY ===
        // Pouze jeden generický parametr TEntity!
        var createDto = new SampleAddEdit
        {
            Name = "Nová ukázka",
            Description = "Popis ukázky",
            IsActive = true
        };

        var createResult = await _entityFacade.CreateAsync<SampleEntity>(createDto);
        if (createResult.Succeeded)
        {
            Console.WriteLine($"Entita vytvořena s ID: {createResult.Data}");
            var entityId = createResult.Data;

            // === NAČTENÍ ENTITY ===
            var getResult = await _entityFacade.GetByIdAsync<SampleEntity>(entityId, useCache: true);
            if (getResult.Succeeded && getResult.Data != null)
            {
                Console.WriteLine($"Entita načtena: {getResult.Data}");

                // === AKTUALIZACE ENTITY ===
                var updateDto = new SampleAddEdit
                {
                    Name = "Aktualizovaná ukázka",
                    Description = "Aktualizovaný popis",
                    IsActive = true
                };

                var updateResult = await _entityFacade.UpdateAsync<SampleEntity>(entityId, updateDto);
                if (updateResult.Succeeded)
                {
                    Console.WriteLine("Entita úspěšně aktualizována");

                    // === SMAZÁNÍ ENTITY ===
                    var deleteResult = await _entityFacade.DeleteAsync<SampleEntity>(entityId);
                    if (deleteResult.Succeeded)
                    {
                        Console.WriteLine("Entita úspěšně smazána");
                    }
                }
            }
        }
    }

    /// <summary>
    /// Ukázka dotazů s filtrováním pomocí specifikací
    /// </summary>
    public async Task QueryWithSpecificationExampleAsync()
    {
        // === ZÍSKÁNÍ VŠECH AKTIVNÍCH ENTIT ===
        var activeSpec = new SampleSpecifications.ActiveSamplesSpecification();
        var allActiveResult = await _entityFacade.GetAllAsync<SampleEntity>(
            specification: activeSpec, 
            useCache: true);

        if (allActiveResult.Succeeded)
        {
            Console.WriteLine($"Nalezeno {allActiveResult.Data.Count()} aktivních entit");
        }

        // === VYHLEDÁVÁNÍ PODLE NÁZVU ===
        var searchSpec = new SampleSpecifications.SamplesByNameSpecification("test");
        var searchResult = await _entityFacade.GetAllAsync<SampleEntity>(
            specification: searchSpec, 
            useCache: false);

        if (searchResult.Succeeded)
        {
            Console.WriteLine($"Nalezeno {searchResult.Data.Count()} entit obsahujících 'test'");
        }

        // === STRÁNKOVANÉ VÝSLEDKY ===
        var pagedResult = await _entityFacade.GetPagedAsync<SampleEntity>(
            pageNumber: 1, 
            pageSize: 10, 
            specification: activeSpec, 
            useCache: true);

        if (pagedResult.Succeeded)
        {
            Console.WriteLine($"Stránka 1: {pagedResult.Items.Count()} z {pagedResult.TotalCount} entit");
        }
    }

    /// <summary>
    /// Ukázka silně typovaných dotazů pro pokročilé použití
    /// </summary>
    public async Task StronglyTypedQueryExampleAsync()
    {
        // Pro pokročilé použití můžeme stále specifikovat TDto
        var activeSpec = new SampleSpecifications.ActiveSamplesSpecification();
        
        var typedResult = await _entityFacade.GetAllAsync<SampleEntity, SampleDto>(
            specification: activeSpec, 
            useCache: true);

        if (typedResult.Succeeded)
        {
            // Zde máme silně typovaný List<SampleDto>
            foreach (var sample in typedResult.Data)
            {
                Console.WriteLine($"Sample: {sample.Name} - {sample.Description}");
            }
        }

        var typedPagedResult = await _entityFacade.GetPagedAsync<SampleEntity, SampleDto>(
            pageNumber: 1, 
            pageSize: 5, 
            specification: activeSpec);

        if (typedPagedResult.Succeeded)
        {
            // Zde máme silně typovaný PagedResult<SampleDto>
            Console.WriteLine($"Stránkované výsledky: {typedPagedResult.Items.Count} z {typedPagedResult.TotalCount}");
        }
    }

    /// <summary>
    /// Ukázka práce s registry typů
    /// </summary>
    public async Task TypeRegistryExampleAsync()
    {
        // Kontrola, zda je entita registrována
        if (_entityFacade.IsEntityRegistered<SampleEntity>())
        {
            Console.WriteLine("SampleEntity je registrována v registry");

            // Získání informací o typech
            var typeInfo = _entityFacade.GetEntityTypeInfo<SampleEntity>();
            if (typeInfo != null)
            {
                Console.WriteLine($"Entity Type: {typeInfo.EntityType.Name}");
                Console.WriteLine($"DTO Type: {typeInfo.DtoType.Name}");
                Console.WriteLine($"Edit DTO Type: {typeInfo.EditDtoType.Name}");
                Console.WriteLine($"Key Type: {typeInfo.KeyType.Name}");
            }
        }
        else
        {
            Console.WriteLine("SampleEntity není registrována - nelze použít fasádu");
        }
    }

    /// <summary>
    /// Ukázka kombinace operací v jednom workflow
    /// </summary>
    public async Task CompleteWorkflowExampleAsync()
    {
        Console.WriteLine("=== Kompletní workflow s EntityFacade ===");

        try
        {
            // 1. Vytvoření několika entit
            var entities = new[]
            {
                new SampleAddEdit { Name = "Entita 1", Description = "První entita", IsActive = true },
                new SampleAddEdit { Name = "Entita 2", Description = "Druhá entita", IsActive = false },
                new SampleAddEdit { Name = "Test entita", Description = "Testovací entita", IsActive = true }
            };

            var createdIds = new List<object>();
            foreach (var entity in entities)
            {
                var result = await _entityFacade.CreateAsync<SampleEntity>(entity);
                if (result.Succeeded)
                {
                    createdIds.Add(result.Data);
                    Console.WriteLine($"Vytvořena entita s ID: {result.Data}");
                }
            }

            // 2. Načtení aktivních entit
            var activeSpec = new SampleSpecifications.ActiveSamplesSpecification();
            var activeEntities = await _entityFacade.GetAllAsync<SampleEntity>(activeSpec, useCache: true);
            
            if (activeEntities.Succeeded)
            {
                Console.WriteLine($"Nalezeno {activeEntities.Data.Count()} aktivních entit");
            }

            // 3. Vyhledání entit obsahujících "Test"
            var searchSpec = new SampleSpecifications.SamplesByNameSpecification("Test");
            var searchResults = await _entityFacade.GetAllAsync<SampleEntity>(searchSpec);
            
            if (searchResults.Succeeded)
            {
                Console.WriteLine($"Nalezeno {searchResults.Data.Count()} entit obsahujících 'Test'");
            }

            // 4. Stránkované načtení
            var pagedResults = await _entityFacade.GetPagedAsync<SampleEntity>(1, 2, activeSpec);
            if (pagedResults.Succeeded)
            {
                Console.WriteLine($"Stránka 1 (velikost 2): {pagedResults.Items.Count()} z {pagedResults.TotalCount} entit");
            }

            // 5. Úklid - smazání vytvořených entit
            foreach (var id in createdIds)
            {
                var deleteResult = await _entityFacade.DeleteAsync<SampleEntity>(id);
                if (deleteResult.Succeeded)
                {
                    Console.WriteLine($"Smazána entita s ID: {id}");
                }
            }

            Console.WriteLine("=== Workflow dokončen ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Chyba ve workflow: {ex.Message}");
        }
    }
}

/// <summary>
/// Porovnání starého a nového přístupu
/// </summary>
public class FacadeComparisonExample
{
    /// <summary>
    /// STARÝ PŘÍSTUP - mnoho generických parametrů
    /// </summary>
    public void OldApproach()
    {
        // Musíme specifikovat všechny typy
        // var command = new CreateEntityCommand<SampleEntity, SampleAddEdit, int> { ... };
        // var query = new GetAllEntitiesQuery<SampleDto, SampleEntity> { ... };
        // var pagedQuery = new GetPagedEntitiesQuery<SampleDto, SampleEntity> { ... };
    }

    /// <summary>
    /// NOVÝ PŘÍSTUP - pouze jeden generický parametr
    /// </summary>
    public async Task NewApproach(IEntityFacade facade)
    {
        // Pouze TEntity - ostatní typy se odvodí automaticky!
        var createResult = await facade.CreateAsync<SampleEntity>(new SampleAddEdit());
        var allResults = await facade.GetAllAsync<SampleEntity>();
        var pagedResults = await facade.GetPagedAsync<SampleEntity>(1, 10);
        
        // Pro pokročilé použití stále můžeme specifikovat TDto
        var typedResults = await facade.GetAllAsync<SampleEntity, SampleDto>();
    }
}
