using SharedKernel.Domain;
using Xunit;

namespace SharedKernel.Tests;

/// <summary>
/// Testy pro ověření správné práce s DateTimeOffset v SharedKernel.
/// </summary>
public class DateTimeOffsetTests
{
    /// <summary>
    /// Test ověřuje, že DomainEvent používá DateTimeOffset místo DateTime.
    /// </summary>
    [Fact]
    public void DomainEvent_Should_Use_DateTimeOffset()
    {
        // Arrange & Act
        var domainEvent = new TestDomainEvent();
        
        // Assert
        Assert.IsType<DateTimeOffset>(domainEvent.DateOccurred);
        Assert.True(domainEvent.DateOccurred <= DateTimeOffset.UtcNow);
        Assert.True(domainEvent.DateOccurred > DateTimeOffset.UtcNow.AddSeconds(-1));
    }

    /// <summary>
    /// Test ověřuje, že ITrackableEntity používá DateTimeOffset pro CreatedAt a ModifiedAt.
    /// </summary>
    [Fact]
    public void TrackableEntity_Should_Use_DateTimeOffset()
    {
        // Arrange & Act
        var entity = new TestTrackableEntity { Id = 1 };
        var now = DateTimeOffset.UtcNow;

        entity.CreatedAt = now;
        entity.ModifiedAt = now.AddMinutes(5);

        // Assert
        Assert.True(entity.CreatedAt.HasValue);
        Assert.True(entity.ModifiedAt.HasValue);
        Assert.IsType<DateTimeOffset>(entity.CreatedAt.Value);
        Assert.IsType<DateTimeOffset>(entity.ModifiedAt.Value);
        Assert.Equal(now, entity.CreatedAt);
        Assert.Equal(now.AddMinutes(5), entity.ModifiedAt);
    }

    /// <summary>
    /// Test ověřuje, že ISoftDelete používá DateTimeOffset pro DeletedAt.
    /// </summary>
    [Fact]
    public void SoftDeleteEntity_Should_Use_DateTimeOffset()
    {
        // Arrange & Act
        var entity = new TestSoftDeleteEntity { Id = 1 };
        var now = DateTimeOffset.UtcNow;

        entity.DeletedAt = now;

        // Assert
        Assert.True(entity.DeletedAt.HasValue);
        Assert.IsType<DateTimeOffset>(entity.DeletedAt.Value);
        Assert.Equal(now, entity.DeletedAt);
    }

    /// <summary>
    /// Test ověřuje, že kombinovaná entita používá DateTimeOffset pro všechny časové vlastnosti.
    /// </summary>
    [Fact]
    public void TrackableSoftDeleteEntity_Should_Use_DateTimeOffset()
    {
        // Arrange & Act
        var entity = new TestTrackableSoftDeleteEntity { Id = 1 };
        var now = DateTimeOffset.UtcNow;

        entity.CreatedAt = now;
        entity.ModifiedAt = now.AddMinutes(5);
        entity.DeletedAt = now.AddMinutes(10);

        // Assert
        Assert.True(entity.CreatedAt.HasValue);
        Assert.True(entity.ModifiedAt.HasValue);
        Assert.True(entity.DeletedAt.HasValue);
        Assert.IsType<DateTimeOffset>(entity.CreatedAt.Value);
        Assert.IsType<DateTimeOffset>(entity.ModifiedAt.Value);
        Assert.IsType<DateTimeOffset>(entity.DeletedAt.Value);
        Assert.Equal(now, entity.CreatedAt);
        Assert.Equal(now.AddMinutes(5), entity.ModifiedAt);
        Assert.Equal(now.AddMinutes(10), entity.DeletedAt);
    }

    /// <summary>
    /// Test ověřuje, že DateTimeOffset správně zachovává informace o časovém pásmu.
    /// </summary>
    [Fact]
    public void DateTimeOffset_Should_Preserve_Timezone_Information()
    {
        // Arrange
        var utcTime = DateTimeOffset.UtcNow;
        var localTime = DateTimeOffset.Now;
        var specificTime = new DateTimeOffset(2025, 7, 31, 12, 0, 0, TimeSpan.FromHours(2));
        
        // Act & Assert
        Assert.Equal(TimeSpan.Zero, utcTime.Offset);
        Assert.NotEqual(TimeSpan.Zero, localTime.Offset); // Může být různé podle lokálního nastavení
        Assert.Equal(TimeSpan.FromHours(2), specificTime.Offset);
        
        // Ověření, že všechny časy lze převést na UTC
        Assert.True(utcTime.UtcDateTime != default);
        Assert.True(localTime.UtcDateTime != default);
        Assert.True(specificTime.UtcDateTime != default);
    }
}

/// <summary>
/// Testovací implementace DomainEvent pro účely testování.
/// </summary>
public class TestDomainEvent : DomainEvent
{
}

/// <summary>
/// Testovací implementace ITrackableEntity pro účely testování.
/// </summary>
public class TestTrackableEntity : BaseTrackableEntity<int>
{
}

/// <summary>
/// Testovací implementace ISoftDelete pro účely testování.
/// </summary>
public class TestSoftDeleteEntity : BaseSoftDeleteEntity<int>
{
}

/// <summary>
/// Testovací implementace kombinované entity pro účely testování.
/// </summary>
public class TestTrackableSoftDeleteEntity : BaseTrackableSoftDeleteEntity<int>
{
}

/// <summary>
/// Testy pro konfigurovatelný cache key provider.
/// </summary>
public class CacheKeyProviderTests
{
    [Fact]
    public void CacheKeyProvider_WithDefaultConfiguration_ShouldUseDefaultPrefix()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>())
            .Build();

        var provider = new TestCacheKeyProvider(configuration);

        // Act & Assert
        Assert.Equal("DataCapture", provider.AppPrefix);
        Assert.Equal("_", provider.Separator);
    }

    [Fact]
    public void CacheKeyProvider_WithCustomConfiguration_ShouldUseCustomPrefix()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "MyApp",
                ["Cache:Separator"] = "-"
            })
            .Build();

        var provider = new TestCacheKeyProvider(configuration);

        // Act & Assert
        Assert.Equal("MyApp", provider.AppPrefix);
        Assert.Equal("-", provider.Separator);
    }

    [Fact]
    public void WithPrefix_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();

        var provider = new TestCacheKeyProvider(configuration);

        // Act
        var result = provider.WithPrefix("TestKey");

        // Assert
        Assert.Equal("TestApp_TestKey", result);
    }

    [Fact]
    public void ForEntity_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();

        var provider = new TestCacheKeyProvider(configuration);

        // Act
        var result = provider.ForEntity("SampleEntity", "GetAll");

        // Assert
        Assert.Equal("TestApp_GetAll_SampleEntity", result);
    }

    [Fact]
    public void ForPagedQuery_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();

        var provider = new TestCacheKeyProvider(configuration);

        // Act
        var result = provider.ForPagedQuery("SampleEntity", 2, 20, "Name", true);

        // Assert
        Assert.Equal("TestApp_GetPaged_SampleEntity_Page2_Size20_SortNameDesc", result);
    }

    [Fact]
    public void ForUser_ShouldCreateCorrectKey()
    {
        // Arrange
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Cache:Prefix"] = "TestApp"
            })
            .Build();

        var provider = new TestCacheKeyProvider(configuration);

        // Act
        var result = provider.ForUser("user123", "Profile");

        // Assert
        Assert.Equal("TestApp_User_user123_Profile", result);
    }
}

/// <summary>
/// Testovací implementace CacheKeyProvider pro účely testování.
/// </summary>
public class TestCacheKeyProvider : SharedKernel.Abstractions.ICacheKeyProvider
{
    private readonly string _appPrefix;
    private readonly string _separator;

    public TestCacheKeyProvider(Microsoft.Extensions.Configuration.IConfiguration configuration)
    {
        _appPrefix = configuration["Cache:Prefix"] ?? SharedKernel.Constants.CacheKeys.DefaultAppPrefix;
        _separator = configuration["Cache:Separator"] ?? SharedKernel.Constants.CacheKeys.Separator;
    }

    public string AppPrefix => _appPrefix;
    public string Separator => _separator;

    public string WithPrefix(string key) => $"{AppPrefix}{Separator}{key}";

    public string ForEntity(string entityName, string operation)
        => WithPrefix($"{operation}{Separator}{entityName}");

    public string ForPagedQuery(string entityName, int pageNumber, int pageSize,
        string? sortBy = null, bool sortDescending = false)
    {
        var key = $"GetPaged{Separator}{entityName}{Separator}Page{pageNumber}{Separator}Size{pageSize}";

        if (!string.IsNullOrEmpty(sortBy))
        {
            key += $"{Separator}Sort{sortBy}";
            if (sortDescending)
                key += "Desc";
        }

        return WithPrefix(key);
    }

    public string ForUser(string userId, string dataType)
        => WithPrefix($"User{Separator}{userId}{Separator}{dataType}");
}
