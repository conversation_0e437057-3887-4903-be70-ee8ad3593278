using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RefactorBusinessRuleEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "ModifiedAt",
                table: "BusinessRules");

            migrationBuilder.DropColumn(
                name: "ModifiedBy",
                table: "BusinessRules");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "BusinessRules",
                type: "BLOB",
                rowVersion: true,
                nullable: false,
                comment: "Verze řádku pro optimistické zamykání",
                oldClrType: typeof(byte[]),
                oldType: "BLOB");

            migrationBuilder.CreateIndex(
                name: "IX_SampleEntity_Name_Age_Composite",
                table: "SampleEntity",
                columns: new[] { "Name", "Age" });

            migrationBuilder.CreateIndex(
                name: "IX_SampleEntity_Name_Unique",
                table: "SampleEntity",
                column: "Name",
                unique: true);

            migrationBuilder.AddCheckConstraint(
                name: "CK_SampleEntity_Age_Range",
                table: "SampleEntity",
                sql: "[Age] >= 0 AND [Age] <= 150");

            migrationBuilder.AddCheckConstraint(
                name: "CK_SampleEntity_DateOfBirth_Range",
                table: "SampleEntity",
                sql: "[DateOfBirth] >= '1900-01-01' AND [DateOfBirth] <= GETDATE()");

            migrationBuilder.AddCheckConstraint(
                name: "CK_SampleEntity_Name_NotEmpty",
                table: "SampleEntity",
                sql: "([IsActive] = 0 OR ([IsActive] = 1 AND [Name] IS NOT NULL AND LEN(TRIM([Name])) > 0))");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_SampleEntity_Name_Age_Composite",
                table: "SampleEntity");

            migrationBuilder.DropIndex(
                name: "IX_SampleEntity_Name_Unique",
                table: "SampleEntity");

            migrationBuilder.DropCheckConstraint(
                name: "CK_SampleEntity_Age_Range",
                table: "SampleEntity");

            migrationBuilder.DropCheckConstraint(
                name: "CK_SampleEntity_DateOfBirth_Range",
                table: "SampleEntity");

            migrationBuilder.DropCheckConstraint(
                name: "CK_SampleEntity_Name_NotEmpty",
                table: "SampleEntity");

            migrationBuilder.AlterColumn<byte[]>(
                name: "RowVersion",
                table: "BusinessRules",
                type: "BLOB",
                nullable: false,
                oldClrType: typeof(byte[]),
                oldType: "BLOB",
                oldRowVersion: true,
                oldComment: "Verze řádku pro optimistické zamykání");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "BusinessRules",
                type: "TEXT",
                nullable: true,
                comment: "Datum a čas vytvoření záznamu");

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "BusinessRules",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                comment: "Identifikátor uživatele, který záznam vytvořil");

            migrationBuilder.AddColumn<DateTime>(
                name: "ModifiedAt",
                table: "BusinessRules",
                type: "TEXT",
                nullable: true,
                comment: "Datum a čas poslední aktualizace záznamu");

            migrationBuilder.AddColumn<string>(
                name: "ModifiedBy",
                table: "BusinessRules",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                comment: "Identifikátor uživatele, který záznam naposledy aktualizoval");
        }
    }
}
