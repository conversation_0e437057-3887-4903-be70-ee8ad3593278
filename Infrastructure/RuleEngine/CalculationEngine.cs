using System.Collections.Concurrent;
using System.Linq.Expressions;
using Infrastructure.RuleEngine.Exceptions;
using Infrastructure.RuleEngine.Validation;
using Microsoft.Extensions.Logging;

namespace Infrastructure.RuleEngine;

/// <summary>
/// Zjednodušený engine pro vykonávání obchodních pravidel.
/// Zaměřený na jednoduchost a spolehlivost pro technické API.
/// </summary>
public class CalculationEngine
{
    private readonly IExpressionBuilder _builder;
    private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;
    private readonly ILogger<CalculationEngine> _logger;

    // Thread-safe cache pro kompilovaná pravidla
    private readonly ConcurrentDictionary<Guid, Func<object, object>> _compiledRules = new();

    public CalculationEngine(
        IExpressionBuilder builder,
        IReadOnlyDictionary<string, Type> entityTypeMap,
        ILogger<CalculationEngine> logger)
    {
        _builder = builder ?? throw new ArgumentNullException(nameof(builder));
        _entityTypeMap = entityTypeMap ?? throw new ArgumentNullException(nameof(entityTypeMap));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Vykoná obchodní pravidlo na zadané entitě.
    /// </summary>
    /// <param name="rule">Pravidlo k vykonání</param>
    /// <param name="entity">Entita pro vyhodnocení</param>
    /// <returns>Výsledek vyhodnocení pravidla</returns>
    /// <exception cref="ArgumentNullException">Pokud rule nebo entity je null</exception>
    /// <exception cref="RuleExecutionException">Pokud dojde k chybě při vykonávání pravidla</exception>
    public object Execute(BusinessRule rule, object entity)
    {
        if (rule == null) throw new ArgumentNullException(nameof(rule));
        if (entity == null) throw new ArgumentNullException(nameof(entity));

        _logger.LogDebug("Spouštím vykonávání pravidla '{RuleName}' (ID: {RuleId}) na entitě typu {EntityType}",
            rule.Name, rule.Id, entity.GetType().Name);

        try
        {
            var startTime = DateTime.UtcNow;
            var compiledRule = GetOrCompileRule(rule);
            var result = compiledRule(entity);
            var executionTime = DateTime.UtcNow - startTime;

            _logger.LogDebug("Pravidlo '{RuleName}' úspěšně vykonáno za {ExecutionTime}ms. Výsledek: {Result}",
                rule.Name, executionTime.TotalMilliseconds, result);

            return result;
        }
        catch (Exception ex) when (!(ex is ArgumentNullException))
        {
            _logger.LogError(ex, "Chyba při vykonávání pravidla '{RuleName}' (ID: {RuleId}): {ErrorMessage}",
                rule.Name, rule.Id, ex.Message);

            throw new RuleExecutionException(
                $"Chyba při vykonávání pravidla '{rule.Name}' (ID: {rule.Id}): {ex.Message}",
                ex);
        }
    }

    /// <summary>
    /// Validuje syntaktickou správnost pravidla bez jeho vykonání.
    /// </summary>
    /// <param name="rule">Pravidlo k validaci</param>
    /// <returns>Výsledek validace</returns>
    public RuleValidationResult ValidateRule(BusinessRule rule)
    {
        _logger.LogDebug("Validuji pravidlo '{RuleName}' (ID: {RuleId})",
            rule?.Name ?? "null", rule?.Id ?? Guid.Empty);

        if (rule == null)
        {
            _logger.LogWarning("Pokus o validaci null pravidla");
            return RuleValidationResult.Invalid("Pravidlo nesmí být null.");
        }

        if (rule.RootNode == null)
        {
            _logger.LogWarning("Pravidlo '{RuleName}' nemá kořenový uzel", rule.Name);
            return RuleValidationResult.Invalid("Pravidlo musí obsahovat kořenový uzel.");
        }

        if (string.IsNullOrWhiteSpace(rule.TargetEntityName))
        {
            _logger.LogWarning("Pravidlo '{RuleName}' nemá definovanou cílovou entitu", rule.Name);
            return RuleValidationResult.Invalid("Pravidlo musí mít definovanou cílovou entitu.");
        }

        if (!_entityTypeMap.ContainsKey(rule.TargetEntityName))
        {
            _logger.LogWarning("Pravidlo '{RuleName}' odkazuje na neznámou entitu '{EntityName}'",
                rule.Name, rule.TargetEntityName);
            return RuleValidationResult.Invalid($"Neznámá cílová entita: {rule.TargetEntityName}");
        }

        try
        {
            // Pokus o kompilaci pro ověření syntaxe
            var startTime = DateTime.UtcNow;
            CompileRule(rule);
            var validationTime = DateTime.UtcNow - startTime;

            _logger.LogDebug("Pravidlo '{RuleName}' úspěšně validováno za {ValidationTime}ms",
                rule.Name, validationTime.TotalMilliseconds);

            return RuleValidationResult.Valid();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Syntaktická chyba v pravidle '{RuleName}': {ErrorMessage}",
                rule.Name, ex.Message);
            return RuleValidationResult.Invalid($"Syntaktická chyba v pravidle: {ex.Message}");
        }
    }

    private Func<object, object> GetOrCompileRule(BusinessRule rule)
    {
        return _compiledRules.GetOrAdd(rule.Id, _ =>
        {
            _logger.LogDebug("Kompiluji pravidlo '{RuleName}' (ID: {RuleId}) - není v cache",
                rule.Name, rule.Id);

            var startTime = DateTime.UtcNow;
            var compiledRule = CompileRule(rule);
            var compilationTime = DateTime.UtcNow - startTime;

            _logger.LogDebug("Pravidlo '{RuleName}' zkompilováno za {CompilationTime}ms a uloženo do cache",
                rule.Name, compilationTime.TotalMilliseconds);

            return compiledRule;
        });
    }

    /// <summary>
    /// Invaliduje zkompilované pravidlo z cache.
    /// Používá se při aktualizaci nebo smazání pravidla.
    /// </summary>
    /// <param name="ruleId">ID pravidla k invalidaci</param>
    public void InvalidateRule(Guid ruleId)
    {
        if (_compiledRules.TryRemove(ruleId, out _))
        {
            _logger.LogDebug("Pravidlo s ID {RuleId} bylo odstraněno z cache", ruleId);
        }
    }

    /// <summary>
    /// Vymaže celou cache kompilovaných pravidel.
    /// </summary>
    public void ClearCache()
    {
        var count = _compiledRules.Count;
        _compiledRules.Clear();
        _logger.LogInformation("Cache kompilovaných pravidel byla vymazána ({Count} pravidel)", count);
    }

    private Func<object, object> CompileRule(BusinessRule rule)
    {
        if (!_entityTypeMap.TryGetValue(rule.TargetEntityName, out var entityType))
        {
            throw new InvalidOperationException($"Neznámý typ entity: {rule.TargetEntityName}");
        }

        var paramEntity = Expression.Parameter(entityType, "entity");
        var body = _builder.Build(rule.RootNode, paramEntity);
        var result = Expression.Convert(body, typeof(object));

        // Vytvoříme wrapper lambda, která přijme object a převede ho na správný typ
        var objectParam = Expression.Parameter(typeof(object), "obj");
        var convertedParam = Expression.Convert(objectParam, entityType);
        var bodyWithConversion = Expression.Invoke(
            Expression.Lambda(result, paramEntity),
            convertedParam);

        var lambda = Expression.Lambda<Func<object, object>>(bodyWithConversion, objectParam);
        return lambda.Compile();
    }



    /// <summary>
    /// Ověří, zda je entita podporována v rule engine.
    /// </summary>
    /// <param name="entityName">Název entity</param>
    /// <returns>True pokud je entita podporována</returns>
    public bool IsEntitySupported(string entityName)
    {
        return !string.IsNullOrWhiteSpace(entityName) && _entityTypeMap.ContainsKey(entityName);
    }

    /// <summary>
    /// Získá seznam všech podporovaných entit.
    /// </summary>
    /// <returns>Kolekce názvů podporovaných entit</returns>
    public IEnumerable<string> GetSupportedEntities()
    {
        return _entityTypeMap.Keys;
    }
}