using System.ComponentModel.DataAnnotations;

namespace Infrastructure.RuleEngine;

/// <summary>
/// Technická entita reprezentuj<PERSON><PERSON><PERSON> obchodní pravidlo pro výpočty a validace.
/// Obsahuje definici pravidla ve formě stromu uzlů (RuleNode).
/// Jedná se o ryze technickou entitu bez trackování změn.
/// </summary>
public class BusinessRule
{
    /// <summary>
    /// Jedinečný identifikátor pravidla.
    /// </summary>
    [Key]
    public Guid Id { get; set; }

    /// <summary>
    /// Verze řádku pro optimistické zamykání.
    /// </summary>
    [Timestamp]
    public byte[] RowVersion { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// Název pravidla pro identifikaci uživatelem.
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// Popis pravidla vysvětlu<PERSON><PERSON><PERSON><PERSON> jeho <PERSON> a použití.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Název cílové entity, na kterou se pravidlo aplikuje.
    /// </summary>
    public required string TargetEntityName { get; set; }

    /// <summary>
    /// Název vlastnosti cílové entity, která bude výsledkem pravidla.
    /// </summary>
    public string? TargetProperty { get; set; }

    /// <summary>
    /// Verze schématu pravidla pro zajištění kompatibility při změnách.
    /// </summary>
    public string SchemaVersion { get; set; } = "1.0";

    /// <summary>
    /// Kořenový uzel stromu pravidla obsahující logiku výpočtu.
    /// </summary>
    public required RuleNode RootNode { get; set; }

    /// <summary>
    /// Určuje, zda je pravidlo aktivní a mělo by být vykonáváno.
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Interní poznámky pro vývojáře.
    /// </summary>
    public string? InternalNotes { get; set; }
}
