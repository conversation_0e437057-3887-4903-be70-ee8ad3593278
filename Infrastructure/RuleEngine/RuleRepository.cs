using Application.Abstraction;
using Infrastructure.RuleEngine.API;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.RuleEngine;

/// <summary>
/// Implementace IRuleRepository pro správu obchodních pravidel v databázi.
/// Poskytuje CRUD operace pro BusinessRule entity.
/// </summary>
public class RuleRepository : IRuleRepository
{
    private readonly IApplicationDbContext _context;

    /// <summary>
    /// Inicializuje novou instanci RuleRepository.
    /// </summary>
    /// <param name="context">Databázový kontext</param>
    public RuleRepository(IApplicationDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// Získá všechna obchodní pravidla.
    /// </summary>
    /// <returns>Kolekce všech pravidel</returns>
    public async Task<IEnumerable<BusinessRule>> GetAllAsync()
    {
        return await _context.Set<BusinessRule>()
            .Where(r => r.IsActive)
            .OrderBy(r => r.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Získá obchodní pravidlo podle ID.
    /// </summary>
    /// <param name="id">ID pravidla</param>
    /// <returns>Nalezené pravidlo nebo null</returns>
    public async Task<BusinessRule?> GetByIdAsync(Guid id)
    {
        return await _context.Set<BusinessRule>()
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    /// <summary>
    /// Přidá nové obchodní pravidlo.
    /// </summary>
    /// <param name="rule">Pravidlo k přidání</param>
    public async Task AddAsync(BusinessRule rule)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));

        // Nastavíme ID pokud není nastaveno
        if (rule.Id == Guid.Empty)
            rule.Id = Guid.NewGuid();

        _context.Set<BusinessRule>().Add(rule);
        await _context.SaveChangesAsync(CancellationToken.None);
    }

    /// <summary>
    /// Aktualizuje existující obchodní pravidlo.
    /// </summary>
    /// <param name="rule">Pravidlo k aktualizaci</param>
    public async Task UpdateAsync(BusinessRule rule)
    {
        if (rule == null)
            throw new ArgumentNullException(nameof(rule));

        var existingRule = await _context.Set<BusinessRule>()
            .FirstOrDefaultAsync(r => r.Id == rule.Id);

        if (existingRule == null)
            throw new InvalidOperationException($"Pravidlo s ID {rule.Id} nebylo nalezeno.");

        // Aktualizujeme vlastnosti
        existingRule.Name = rule.Name;
        existingRule.Description = rule.Description;
        existingRule.TargetEntityName = rule.TargetEntityName;
        existingRule.TargetProperty = rule.TargetProperty;
        existingRule.SchemaVersion = rule.SchemaVersion;
        existingRule.RootNode = rule.RootNode;
        existingRule.IsActive = rule.IsActive;
        existingRule.InternalNotes = rule.InternalNotes;

        // UpdatedAt se nastavuje automaticky v TrackableEntityInterceptor
        await _context.SaveChangesAsync(CancellationToken.None);
    }

    /// <summary>
    /// Smaže obchodní pravidlo podle ID.
    /// </summary>
    /// <param name="id">ID pravidla ke smazání</param>
    public async Task DeleteAsync(Guid id)
    {
        var rule = await _context.Set<BusinessRule>()
            .FirstOrDefaultAsync(r => r.Id == id);

        if (rule == null)
            throw new InvalidOperationException($"Pravidlo s ID {id} nebylo nalezeno.");

        _context.Set<BusinessRule>().Remove(rule);
        await _context.SaveChangesAsync(CancellationToken.None);
    }

    /// <summary>
    /// Získá aktivní pravidla pro konkrétní entitu.
    /// </summary>
    /// <param name="entityName">Název cílové entity</param>
    /// <returns>Kolekce aktivních pravidel pro entitu</returns>
    public async Task<IEnumerable<BusinessRule>> GetActiveRulesForEntityAsync(string entityName)
    {
        if (string.IsNullOrWhiteSpace(entityName))
            throw new ArgumentException("Název entity nesmí být prázdný.", nameof(entityName));

        return await _context.Set<BusinessRule>()
            .Where(r => r.IsActive && r.TargetEntityName == entityName)
            .OrderBy(r => r.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Ověří, zda existuje pravidlo se zadaným názvem (pro validaci duplicit).
    /// </summary>
    /// <param name="name">Název pravidla</param>
    /// <param name="excludeId">ID pravidla k vyloučení z kontroly (pro update)</param>
    /// <returns>True pokud pravidlo s názvem existuje</returns>
    public async Task<bool> ExistsWithNameAsync(string name, Guid? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            return false;

        var query = _context.Set<BusinessRule>().Where(r => r.Name == name);

        if (excludeId.HasValue)
            query = query.Where(r => r.Id != excludeId.Value);

        return await query.AnyAsync();
    }
}
