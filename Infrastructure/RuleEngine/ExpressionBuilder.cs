using System;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace Infrastructure.RuleEngine
{
    /// <summary>
    /// Implementation of expression builder from RuleNode structure.
    /// Compiles rules into Expression Trees for high performance.
    /// </summary>
    public class ExpressionBuilder : IExpressionBuilder
    {
        private readonly IRuleDataProvider _dataProvider;
        private readonly IReadOnlyDictionary<string, Type> _entityTypeMap;
        private readonly IReadOnlyDictionary<string, PropertyInfo> _propertyMetadata;
        private readonly Action<RuleNode, Expression> _logHook;

        public ExpressionBuilder(
            IRuleDataProvider dataProvider,
            IReadOnlyDictionary<string, Type> entityTypeMap,
            IReadOnlyDictionary<string, PropertyInfo> propertyMetadata = null,
            Action<RuleNode, Expression> logHook = null)
        {
            _dataProvider = dataProvider;
            _entityTypeMap = entityTypeMap;
            _propertyMetadata = propertyMetadata;
            _logHook = logHook;
        }

        public Expression Build(RuleNode node, ParameterExpression param)
        {
            Expression expr = node switch
            {
                ConstantNode c => BuildConstant(c),
                SourceValueNode sv => BuildSource(sv, param),
                OperationNode op => BuildOperation(op, param),
                LookupNode lk => BuildLookup(lk, param),
                AggregationNode ag => BuildAggregation(ag, param),
                RelatedAggregationNode ra => BuildRelatedAggregation(ra, param),
                _ => throw new NotSupportedException($"Unsupported node type: {node.GetType().Name}")
            };

            _logHook?.Invoke(node, expr);
            return expr;
        }

        private Expression BuildConstant(ConstantNode c)
        {
            object value = c.DataType switch
            {
                ValueType.Integer  => int.Parse(c.Value, CultureInfo.InvariantCulture),
                ValueType.Decimal  => decimal.Parse(c.Value, CultureInfo.InvariantCulture),
                ValueType.Boolean  => bool.Parse(c.Value),
                ValueType.DateTime => DateTime.Parse(c.Value, CultureInfo.InvariantCulture),
                ValueType.Guid     => Guid.Parse(c.Value),
                _                  => c.Value
            };
            return Expression.Constant(value, value.GetType());
        }

        private Expression BuildOperation(OperationNode op, ParameterExpression param)
        {
            var children = op.Operands.Select(n => Build(n, param)).ToList();
            return op.Operator switch
            {
                OperatorType.Add                  => Expression.Add(children[0], children[1]),
                OperatorType.Subtract             => Expression.Subtract(children[0], children[1]),
                OperatorType.Multiply             => Expression.Multiply(children[0], children[1]),
                OperatorType.Divide               => Expression.Divide(children[0], children[1]),
                OperatorType.Equal                => Expression.Equal(children[0], children[1]),
                OperatorType.NotEqual             => Expression.NotEqual(children[0], children[1]),
                OperatorType.GreaterThan          => Expression.GreaterThan(children[0], children[1]),
                OperatorType.LessThan             => Expression.LessThan(children[0], children[1]),
                OperatorType.GreaterThanOrEqual   => Expression.GreaterThanOrEqual(children[0], children[1]),
                OperatorType.LessThanOrEqual      => Expression.LessThanOrEqual(children[0], children[1]),
                OperatorType.And                  => Expression.AndAlso(AsBool(children[0]), AsBool(children[1])),
                OperatorType.Or                   => Expression.OrElse(AsBool(children[0]), AsBool(children[1])),
                OperatorType.Not                  => Expression.Not(AsBool(children[0])),
                OperatorType.If                   => Expression.Condition(
                                                        AsBool(children[0]),
                                                        Expression.Convert(children[1], children[2].Type),
                                                        children[2]),
                _ => throw new NotSupportedException($"Operator not supported: {op.Operator}")
            };

            static Expression AsBool(Expression e) =>
                e.Type == typeof(bool) ? e : Expression.NotEqual(e, Expression.Constant(null, e.Type));
        }

        private Expression BuildLookup(LookupNode lk, ParameterExpression param)
        {
            var entityType = _entityTypeMap[lk.TargetEntityName];
            var lambdaParam = Expression.Parameter(entityType, "e");
            var body = Build(lk.Condition, lambdaParam);
            var predicate = Expression.Lambda(body, lambdaParam);
            var providerExpr = Expression.Constant(_dataProvider);
            var call = Expression.Call(
                providerExpr,
                nameof(IRuleDataProvider.FindSingle),
                Type.EmptyTypes,
                Expression.Constant(lk.TargetEntityName),
                predicate
            );
            var casted = Expression.Convert(call, entityType);
            return Expression.PropertyOrField(casted, lk.ReturnFieldPath);
        }

        private Expression BuildSource(SourceValueNode sv, ParameterExpression param)
        {
            Expression expr = param;
            foreach (var part in sv.SourcePath.Split('.'))
            {
                if (_propertyMetadata != null && _propertyMetadata.TryGetValue(part, out var prop))
                    expr = Expression.Property(expr, prop);
                else
                    expr = Expression.PropertyOrField(expr, part);
            }
            return expr;
        }

        private Expression BuildAggregation(AggregationNode ag, ParameterExpression param)
        {
            Expression collection = param;
            foreach (var part in ag.CollectionPath.Split('.'))
                collection = Expression.PropertyOrField(collection, part);

            var elementType = collection.Type.GetGenericArguments().First();
            var lambdaParam = Expression.Parameter(elementType, "x");

            // Apply filter if any
            Expression filtered = collection;
            if (ag.Filter != null)
            {
                var filterBody = Build(ag.Filter, lambdaParam);
                var predicate = Expression.Lambda(filterBody, lambdaParam);
                var providerType = typeof(Enumerable);
                var whereMethod = providerType
                    .GetMethods()
                    .First(m => m.Name == "Where" && m.GetParameters().Length == 2)
                    .MakeGenericMethod(elementType);
                filtered = Expression.Call(whereMethod, collection, predicate);
            }

            // Build selector
            Expression selector = lambdaParam;
            if (!string.IsNullOrEmpty(ag.FieldPath))
            {
                selector = ag.FieldPath.Split('.')
                    .Aggregate((Expression)lambdaParam,
                        (expr, part) => Expression.PropertyOrField(expr, part));
            }
            var selectorLambda = Expression.Lambda(selector, lambdaParam);

            // Perform aggregation
            var providerAggType = typeof(Enumerable);
            MethodCallExpression call;
            if (ag.AggregationType == AggregationType.Count)
            {
                var countMethod = providerAggType
                    .GetMethods()
                    .First(m => m.Name == "Count" && m.GetParameters().Length == 1)
                    .MakeGenericMethod(elementType);
                call = Expression.Call(countMethod, filtered);
            }
            else
            {
                var methodName = ag.AggregationType.ToString(); // Sum, Average, Min, Max
                var aggMethod = providerAggType
                    .GetMethods()
                    .First(m => m.Name == methodName &&
                               m.GetParameters().Length == 2)
                    .MakeGenericMethod(elementType);
                call = Expression.Call(aggMethod, filtered, selectorLambda);
            }
            return call;
        }

        /// <summary>
        /// Builds expression for related aggregation using IRuleDataProvider.FindMany
        /// </summary>
        private Expression BuildRelatedAggregation(RelatedAggregationNode node, ParameterExpression param)
        {
            // Retrieve relationship key from source entity
            var keyExpr = Expression.PropertyOrField(param, node.RelationshipProperty);

            // Build predicate: e => e.RelationshipProperty == key
            var targetType = _entityTypeMap[node.TargetEntityName];
            var itemParam = Expression.Parameter(targetType, "e");
            var left = Expression.Equal(
                Expression.PropertyOrField(itemParam, node.RelationshipProperty),
                keyExpr
            );
            Expression whereBody = left;
            if (node.FilterCondition != null)
            {
                var extra = Build(node.FilterCondition, itemParam);
                whereBody = Expression.AndAlso(left, Expression.Convert(extra, typeof(bool)));
            }
            var predicate = Expression.Lambda(whereBody, itemParam);

            // Call dataProvider.FindMany(targetEntity, predicate)
            var providerExpr = Expression.Constant(_dataProvider);
            var findManyMethod = typeof(IRuleDataProvider).GetMethod(nameof(IRuleDataProvider.FindMany));
            var findManyCall = Expression.Call(
                providerExpr,
                findManyMethod!,
                Expression.Constant(node.TargetEntityName),
                Expression.Quote(predicate) // Quote the lambda expression
            );
            // findManyCall returns IEnumerable<object>, need to cast to IEnumerable<targetType>

            // Cast IEnumerable<object> to IEnumerable<targetType>
            var castMethod = typeof(Enumerable)
                .GetMethod(nameof(Enumerable.Cast))!
                .MakeGenericMethod(targetType);
            var castedCollection = Expression.Call(castMethod, findManyCall);

            // Build aggregation over returned collection
            var elementType = targetType;
            var lambdaParam = Expression.Parameter(elementType, "x");

            // Build selector for aggregation field
            Expression selector = lambdaParam;
            Type selectorType = elementType;

            if (!string.IsNullOrEmpty(node.AggregationField))
            {
                selector = node.AggregationField.Split('.')
                    .Aggregate((Expression)lambdaParam, (expr, part) => Expression.PropertyOrField(expr, part));
                selectorType = selector.Type;
            }

            // Handle Count aggregation specially (doesn't need selector type matching)
            if (node.AggregationType == AggregationType.Count)
            {
                // Count with predicate - selector should return bool
                if (!string.IsNullOrEmpty(node.AggregationField))
                {
                    // For Count, we need a predicate that returns bool
                    // This is likely a filter condition, not a field selector
                    var selectorLambda = Expression.Lambda(selector, lambdaParam);
                    var countMethod = typeof(Enumerable)
                        .GetMethods()
                        .First(m => m.Name == "Count" && m.GetParameters().Length == 2)
                        .MakeGenericMethod(elementType);
                    return Expression.Call(countMethod, castedCollection, selectorLambda);
                }
                else
                {
                    // Simple count without predicate
                    var countMethod = typeof(Enumerable)
                        .GetMethods()
                        .First(m => m.Name == "Count" && m.GetParameters().Length == 1)
                        .MakeGenericMethod(elementType);
                    return Expression.Call(countMethod, castedCollection);
                }
            }

            // For other aggregations (Sum, Average, etc.), find the right method
            var selectorLambda2 = Expression.Lambda(selector, lambdaParam);
            var methodName = node.AggregationType.ToString();

            // Find the aggregation method that matches the selector type
            var aggMethod = typeof(Enumerable)
                .GetMethods()
                .Where(m => m.Name == methodName && m.GetParameters().Length == 2)
                .FirstOrDefault(m =>
                {
                    var genericMethod = m.MakeGenericMethod(elementType);
                    var paramType = genericMethod.GetParameters()[1].ParameterType;
                    var expectedSelectorType = typeof(Func<,>).MakeGenericType(elementType, selectorType);
                    return paramType.IsAssignableFrom(expectedSelectorType);
                });

            if (aggMethod == null)
            {
                throw new InvalidOperationException($"Nelze najít agregační metodu {methodName} pro typ {selectorType.Name}");
            }

            var genericAggMethod = aggMethod.MakeGenericMethod(elementType);
            return Expression.Call(genericAggMethod, castedCollection, selectorLambda2);
        }
    }
}
