using Infrastructure.RuleEngine;
using Infrastructure.Conversions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;
using Infrastructure.Persistence.Conversions;

namespace Infrastructure.Persistence.Configurations;

/// <summary>
/// Konfigurace Entity Framework pro BusinessRule entitu.
/// Definuje mapování na databázovou tabulku a serializaci RuleNode jako JSON.
/// </summary>
public class BusinessRuleConfiguration : IEntityTypeConfiguration<BusinessRule>
{
    /// <summary>
    /// Konfiguruje mapování BusinessRule entity.
    /// </summary>
    /// <param name="builder">Builder pro konfiguraci entity</param>
    public void Configure(EntityTypeBuilder<BusinessRule> builder)
    {
        // Název tabulky
        builder.ToTable("BusinessRules");

        // Primary key
        builder.HasKey(e => e.Id);

        // Vlastnosti
        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(200)
            .HasComment("Název obchodního pravidla");

        builder.Property(e => e.Description)
            .HasMaxLength(1000)
            .HasComment("Popis účelu a použití pravidla");

        builder.Property(e => e.TargetEntityName)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("Název cílové entity pro aplikaci pravidla");

        builder.Property(e => e.TargetProperty)
            .HasMaxLength(100)
            .HasComment("Název vlastnosti cílové entity pro výsledek pravidla");

        builder.Property(e => e.SchemaVersion)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue("1.0")
            .HasComment("Verze schématu pravidla pro kompatibilitu");

        builder.Property(e => e.IsActive)
            .IsRequired()
            .HasDefaultValue(true)
            .HasComment("Určuje, zda je pravidlo aktivní");

        builder.Property(e => e.InternalNotes)
            .HasMaxLength(2000)
            .HasComment("Interní poznámky pro vývojáře");

        // RootNode jako JSON s podporou polymorfních typů
        builder.Property(e => e.RootNode)
            .IsRequired()
            .HasConversion(
                v => JsonSerializer.Serialize(v, DefaultJsonSerializerOptions.Options),
                v => JsonSerializer.Deserialize<RuleNode>(v, DefaultJsonSerializerOptions.Options)!)
            .HasColumnType("TEXT")
            .HasComment("Kořenový uzel pravidla serializovaný jako JSON");

        // Indexy pro výkon
        builder.HasIndex(e => e.Name)
            .IsUnique()
            .HasDatabaseName("IX_BusinessRules_Name");

        builder.HasIndex(e => e.TargetEntityName)
            .HasDatabaseName("IX_BusinessRules_TargetEntityName");

        builder.HasIndex(e => e.IsActive)
            .HasDatabaseName("IX_BusinessRules_IsActive");

        builder.HasIndex(e => new { e.TargetEntityName, e.IsActive })
            .HasDatabaseName("IX_BusinessRules_TargetEntity_Active");

        // RowVersion pro optimistické zamykání
        builder.Property(e => e.RowVersion)
            .IsRowVersion()
            .HasComment("Verze řádku pro optimistické zamykání");
    }
}
